
<?php $__env->startSection('page-title'); ?>
    <?php echo e($lead->name); ?>

<?php $__env->stopSection(); ?>

<?php $__env->startPush('css-page'); ?>
    <link rel="stylesheet" href="<?php echo e(asset('css/summernote/summernote-bs4.css')); ?>">
    <link rel="stylesheet" href="<?php echo e(asset('assets/css/plugins/dropzone.min.css')); ?>">
    <style>
        .tab-btn {
            border: 1px solid #0f766e;
            background: #fff;
            color: #0f766e !important;
            padding: 10px 28px;
            border-radius: 14px;
            font-weight: 500;
            font-size: 1rem;
            transition: background 0.2s, color 0.2s, box-shadow 0.2s, border 0.2s;
            box-shadow: 0 2px 6px rgba(0,0,0,0.04);
            outline: none;
        }
        .tab-btn.active, .tab-btn:focus {
            background: none;
            color: #fff !important;
            border: 1px solid transparent;
            box-shadow: 0 4px 12px rgba(6,95,70,0.10);
        }
        .tab-btn:not(.active):hover {
            background: none;
            color: #fff !important;
            border-color: #43b37a;
        }
        .tab-section { display: block; }
        .tab-section.d-none { display: none !important; }
        @media (max-width: 767px) {
            .tab-btn { width: 100%; margin-bottom: 8px; }
            .tab-menu-responsive { flex-direction: column !important; align-items: stretch !important; }
        }
    </style>
    <style>
        .comments-youtube-ui { background: #fff; border-radius: 12px; }
        .comments-youtube-ui .form-control, .comments-youtube-ui textarea { background: #fff; color: #222; border: 1px solid #e0e0e0; }
        .comments-youtube-ui .form-control:focus, .comments-youtube-ui textarea:focus { background: #f8f9fa; color: #222; border-color: #0f766e; }
        .comments-youtube-ui .btn { border-radius: 20px; }
        .comments-youtube-ui .comment-item, .comments-youtube-ui .reply-item { transition: background 0.2s; }
        .comments-youtube-ui .comment-item:hover { background: #f8f9fa; }
        .comments-youtube-ui .reply-item { background: #f5f5f5; border-radius: 8px; }
        @media (max-width: 767px) {
            .comments-youtube-ui { padding: 1rem !important; }
            .comments-youtube-ui .theme-avtar { width: 32px !important; height: 32px !important; font-size: 1rem !important; }
        }
    </style>
<?php $__env->stopPush(); ?>
<?php $__env->startPush('script-page'); ?>
    <script src="<?php echo e(asset('css/summernote/summernote-bs4.js')); ?>"></script>
    <script src="<?php echo e(asset('assets/js/plugins/dropzone-amd-module.min.js')); ?>"></script>
    <script>
        var scrollSpy = new bootstrap.ScrollSpy(document.body, {
            target: '#lead-sidenav',
            offset: 300
        })
        Dropzone.autoDiscover = false;
        myDropzone = new Dropzone("#dropzonewidget", {
            maxFiles: 20,
            maxFilesize: 20, // 20MB max
            parallelUploads: 1,
            filename: false,
            acceptedFiles: ".jpeg,.jpg,.png,.pdf,.doc,.docx,.txt,.xlsx,.xls,.ppt,.pptx,.zip,.rar",
            url: "<?php echo e(route('leads.file.upload', $lead->id)); ?>",
            headers: {
                'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
            },
            init: function() {
                this.on("addedfile", function(file) {
                    // Add loading state
                    $(file.previewElement).addClass('uploading');
                });

                this.on("uploadprogress", function(file, progress) {
                    // Update progress
                    $(file.previewElement).find('.dz-upload').css('width', progress + '%');
                });
            },
            success: function(file, response) {
                $(file.previewElement).removeClass('uploading');

                if (response.is_success) {
                    if (response.status == 1) {
                        show_toastr('success', response.success_msg, 'success');
                    } else {
                        show_toastr('success', 'File uploaded successfully', 'success');
                    }
                    dropzoneBtn(file, response);

                    // Add uploaded file to the table instantly
                    if (response.uploaded_file) {
                        addUploadedFileRow(response.uploaded_file);
                    }
                } else {
                    myDropzone.removeFile(file);
                    show_toastr('error', response.error || 'Upload failed', 'error');
                }
            },
            error: function(file, response) {
                $(file.previewElement).removeClass('uploading');
                myDropzone.removeFile(file);

                let errorMessage = 'Upload failed';

                if (typeof response === 'string') {
                    try {
                        response = JSON.parse(response);
                    } catch (e) {
                        errorMessage = response;
                    }
                }

                if (response && response.error) {
                    errorMessage = response.error;
                } else if (response && response.message) {
                    errorMessage = response.message;
                } else if (file.status === 'error' && file.xhr) {
                    if (file.xhr.status === 413) {
                        errorMessage = 'File too large. Maximum size is 20MB.';
                    } else if (file.xhr.status === 422) {
                        errorMessage = 'Invalid file type or validation error.';
                    } else if (file.xhr.status === 500) {
                        errorMessage = 'Server error. Please try again.';
                    }
                }

                show_toastr('error', errorMessage, 'error');
            },
            maxfilesexceeded: function(file) {
                show_toastr('error', 'Maximum number of files exceeded', 'error');
                this.removeFile(file);
            }
        });
        myDropzone.on("sending", function(file, xhr, formData) {
            formData.append("_token", $('meta[name="csrf-token"]').attr('content'));
            formData.append("lead_id", <?php echo e($lead->id); ?>);

            // Add file validation on client side
            if (file.size > 20 * 1024 * 1024) { // 20MB
                show_toastr('error', 'File size exceeds 20MB limit', 'error');
                myDropzone.removeFile(file);
                return false;
            }
        });

        function dropzoneBtn(file, response) {
            var download = document.createElement('a');
            download.setAttribute('href', response.download);
            download.setAttribute('class', "badge bg-info mx-1");
            download.setAttribute('data-toggle', "tooltip");
            download.setAttribute('data-original-title', "<?php echo e(__('Download')); ?>");
            download.innerHTML = "<i class='ti ti-download'></i>";

            var del = document.createElement('a');
            del.setAttribute('href', response.delete);
            del.setAttribute('class', "badge bg-danger mx-1");
            del.setAttribute('data-toggle', "tooltip");
            del.setAttribute('data-original-title', "<?php echo e(__('Delete')); ?>");
            del.innerHTML = "<i class='ti ti-trash'></i>";

            del.addEventListener("click", function(e) {
                e.preventDefault();
                e.stopPropagation();
                if (confirm("Are you sure ?")) {
                    var btn = $(this);
                    $.ajax({
                        url: btn.attr('href'),
                        data: {
                            _token: $('meta[name="csrf-token"]').attr('content')
                        },
                        type: 'DELETE',
                        success: function(response) {
                            if (response.is_success) {
                                btn.closest('.dz-image-preview').remove();
                            } else {
                                show_toastr('error', response.error, 'error');
                            }
                        },
                        error: function(response) {
                            response = response.responseJSON;
                            if (response.is_success) {
                                show_toastr('error', response.error, 'error');
                            } else {
                                show_toastr('error', response, 'error');
                            }
                        }
                    })
                }
            });

            var html = document.createElement('div');
            html.appendChild(download);
            <?php if(Auth::user()->type != 'client'): ?>
                <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('edit lead')): ?>
                    html.appendChild(del);
                <?php endif; ?>
            <?php endif; ?>

            file.previewTemplate.appendChild(html);
        }

        // Add this helper function after Dropzone config in the same script block
        function addUploadedFileRow(file) {
            // file: {id, file_name, file_path, created_at, download_url, delete_url, ext, size, formatted_size}
            let icon = 'ti-file';
            if(['jpg','jpeg','png','gif'].includes((file.ext||'').toLowerCase())) icon = 'ti ti-photo';
            else if(['pdf'].includes((file.ext||'').toLowerCase())) icon = 'ti ti-file-text';
            else if(['doc','docx'].includes((file.ext||'').toLowerCase())) icon = 'ti ti-file-description';
            else if(['txt'].includes((file.ext||'').toLowerCase())) icon = 'ti ti-file';

            let sizeInfo = file.formatted_size ? `<br><small class="text-muted">${file.formatted_size}</small>` : '';
            let uploaderInfo = `<br><small class="text-muted">by <?php echo e(Auth::user()->name); ?></small>`;

            let row = `<tr>
                <td><i class="ti ${icon} fs-4 text-primary"></i></td>
                <td class="text-break">${file.file_name}${sizeInfo}</td>
                <td><small class="text-muted">just now</small>${uploaderInfo}</td>
                <td>
                    <a href="${file.download_url}" class="btn btn-sm btn-outline-info me-1" title="Download">
                        <i class="ti ti-download"></i>
                    </a>
                    ${(file.can_delete ? `<button type='button' class='btn btn-sm btn-outline-danger delete-file-btn' data-file-id='${file.id}' data-delete-url='${file.delete_url}' title='Delete'><i class='ti ti-trash'></i></button>` : '')}
                </td>
            </tr>`;
            $('#uploaded-files-table tbody').prepend(row);
        }

        // Handle delete button clicks for all files (both existing and newly uploaded)
        $(document).on('click', '.delete-file-btn', function(e) {
            e.preventDefault();

            // Store delete information for the modal
            const deleteUrl = $(this).data('delete-url');
            const fileName = $(this).closest('tr').find('td:nth-child(2)').text().trim().split('\n')[0];
            const row = $(this).closest('tr');

            // Set modal content
            $('#deleteFileName').text(fileName);
            $('#confirmDeleteBtn').data('delete-url', deleteUrl);
            $('#confirmDeleteBtn').data('target-row', row);

            // Show the delete modal
            $('#deleteFileModal').modal('show');
        });

        // Handle confirm delete button in modal
        $('#confirmDeleteBtn').on('click', function() {
            const deleteUrl = $(this).data('delete-url');
            const row = $(this).data('target-row');
            const modal = $('#deleteFileModal');

            // Add loading state to modal button
            $(this).prop('disabled', true).html('<i class="ti ti-loader me-1"></i><?php echo e(__("Deleting...")); ?>');

            $.ajax({
                url: deleteUrl,
                type: 'POST',
                data: {
                    _method: 'DELETE',
                    _token: $('meta[name="csrf-token"]').attr('content')
                },
                success: function(response) {
                    if (response.is_success) {
                        // Hide modal first
                        modal.modal('hide');

                        // Remove row with animation
                        row.fadeOut(300, function() {
                            $(this).remove();
                        });

                        show_toastr('success', response.message || 'File deleted successfully', 'success');
                    } else {
                        show_toastr('error', response.error || 'Failed to delete file', 'error');
                    }
                },
                error: function(xhr) {
                    let errorMessage = 'Failed to delete file';
                    if (xhr.responseJSON && xhr.responseJSON.error) {
                        errorMessage = xhr.responseJSON.error;
                    }
                    show_toastr('error', errorMessage, 'error');
                },
                complete: function() {
                    // Reset modal button state
                    $('#confirmDeleteBtn').prop('disabled', false).html('<i class="ti ti-trash me-1"></i><?php echo e(__("Delete")); ?>');
                }
            });
        });

        // Reset modal data when hidden
        $('#deleteFileModal').on('hidden.bs.modal', function() {
            $('#confirmDeleteBtn').removeData('delete-url').removeData('target-row');
            $('#deleteFileName').text('');
        });

        <?php $__currentLoopData = $lead->files; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $file): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
            <?php if(\Illuminate\Support\Facades\Storage::disk('public')->exists($file->file_path)): ?>
                // Create the mock file:
                var mockFile = {
                    name: "<?php echo e($file->original_name ?? $file->file_name); ?>",
                    size: <?php echo e($file->file_size ?? 0); ?>

                };
                // Call the default addedfile event handler
                myDropzone.emit("addedfile", mockFile);
                // And optionally show the thumbnail of the file:
                myDropzone.emit("thumbnail", mockFile, "<?php echo e(\Illuminate\Support\Facades\Storage::disk('public')->url($file->file_path)); ?>");
                myDropzone.emit("complete", mockFile);

                dropzoneBtn(mockFile, {
                    download: "<?php echo e(route('leads.file.download', [$lead->id, $file->id])); ?>",
                    delete: "<?php echo e(route('leads.file.delete', [$lead->id, $file->id])); ?>"
                });
            <?php endif; ?>
        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>

        <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('edit lead')): ?>
            $('.summernote-simple').on('summernote.blur', function() {

                $.ajax({
                    url: "<?php echo e(route('leads.note.store', $lead->id)); ?>",
                    data: {
                        _token: $('meta[name="csrf-token"]').attr('content'),
                        notes: $(this).val()
                    },
                    type: 'POST',
                    success: function(response) {
                        if (response.is_success) {
                            // show_toastr('Success', response.success,'success');
                        } else {
                            show_toastr('error', response.error, 'error');
                        }
                    },
                    error: function(response) {
                        response = response.responseJSON;
                        if (response.is_success) {
                            show_toastr('error', response.error, 'error');
                        } else {
                            show_toastr('error', response, 'error');
                        }
                    }
                })
            });
        <?php else: ?>
            $('.summernote-simple').summernote('disable');
        <?php endif; ?>

        <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('edit lead task')): ?>
            $(document).on("click", ".task-checkbox", function() {
                var chbox = $(this);
                var lbl = chbox.parent().parent().find('label');

                $.ajax({
                    url: chbox.attr('data-url'),
                    data: {
                        _token: $('meta[name="csrf-token"]').attr('content'),
                        status: chbox.val()
                    },
                    type: 'PUT',
                    success: function(response) {
                        if (response.is_success) {
                            chbox.val(response.status);
                            if (response.status) {
                                lbl.addClass('strike');
                                lbl.find('.badge').removeClass('badge-warning').addClass(
                                    'badge-success');
                            } else {
                                lbl.removeClass('strike');
                                lbl.find('.badge').removeClass('badge-success').addClass(
                                    'badge-warning');
                            }
                            lbl.find('.badge').html(response.status_label);

                            show_toastr('success', response.success);
                        } else {
                            show_toastr('error', response.error);
                        }
                    },
                    error: function(response) {
                        response = response.responseJSON;
                        if (response.is_success) {
                            show_toastr('success', response.success);
                        } else {
                            show_toastr('error', response.error);
                        }
                    }
                })
            });
        <?php endif; ?>
    </script>
    <script>
    $(document).ready(function() {
        // Tab switching logic
        $('.tab-btn').on('click', function() {
            $('.tab-btn').removeClass('active bg-primary bg-gradient text-white');
            $('.tab-btn').css({'color':'#0f766e','background':'#fff'});
            $(this).addClass('active bg-primary bg-gradient text-white');
            $(this).css({'color':'#fff'});
            $('.tab-section').addClass('d-none');
            $('#tab-' + $(this).data('tab')).removeClass('d-none');
        });
        // Optionally, show the first tab by default
        $('.tab-btn[data-tab="followups"]').trigger('click');
        $('.tab-btn').hover(
            function() {
                if (!$(this).hasClass('active')) {
                    $(this).addClass('bg-primary bg-gradient text-white');
                    $(this).css({'color':'#fff'});
                }
            },
            function() {
                if (!$(this).hasClass('active')) {
                    $(this).removeClass('bg-primary bg-gradient text-white');
                    $(this).css({'color':'#0f766e','background':'#fff'});
                }
            }
        );
    });
    </script>
<?php $__env->stopPush(); ?>
<?php $__env->startSection('breadcrumb'); ?>
    <li class="breadcrumb-item"><a href="<?php echo e(route('dashboard')); ?>"><?php echo e(__('Dashboard')); ?></a></li>
    <li class="breadcrumb-item"><a href="<?php echo e(route('leads.index')); ?>"><?php echo e(__('Lead')); ?></a></li>
    <li class="breadcrumb-item"> <?php echo e($lead->name); ?></li>
<?php $__env->stopSection(); ?>
<?php $__env->startSection('action-btn'); ?>
    <div class="float-end">
        <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('convert lead to deal')): ?>
            <?php if(!empty($deal)): ?>
                <a href="<?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('View Deal')): ?> <?php if($deal->is_active): ?> <?php echo e(route('deals.show', $deal->id)); ?> <?php else: ?> # <?php endif; ?> <?php else: ?> # <?php endif; ?>"
                    data-size="lg" data-bs-toggle="tooltip" title=" <?php echo e(__('Already Converted To Deal')); ?>"
                    class="btn btn-sm bg-warning-subtle me-1">
                    <i class="ti ti-exchange"></i>
                </a>
            <?php else: ?>
            <a href="#"
                data-size="lg"
                data-url="<?php echo e(URL::to('leads/' . $lead->id . '/show_convert')); ?>"
                data-ajax-popup="true"
                data-bs-toggle="tooltip"
                title="<?php echo e(__('Convert [' . $lead->subject . '] To Deal')); ?>"
                style="display:inline-flex;align-items:center;justify-content:center;width:40px;height:40px;border-radius:50%;background:linear-gradient(to right, #065f46, #0f766e);color:white;box-shadow:0 4px 6px rgba(0,0,0,0.1);transition:transform 0.2s ease-in-out;"
                onmouseover="this.style.transform='scale(1.1)';"
                onmouseout="this.style.transform='scale(1)';">
                <i class="ti ti-exchange"></i>
            </a>
            <?php endif; ?>
        <?php endif; ?>

        <a href="#"
            data-url="<?php echo e(URL::to('leads/' . $lead->id . '/labels')); ?>"
            data-ajax-popup="true"
            data-size="lg"
            data-bs-toggle="tooltip"
            title="<?php echo e(__('Label')); ?>"
            style="display:inline-flex;align-items:center;justify-content:center;width:40px;height:40px;border-radius:50%;background:linear-gradient(to right, #065f46, #0f766e);color:white;box-shadow:0 4px 6px rgba(0,0,0,0.1);transition:transform 0.2s ease-in-out;"
            onmouseover="this.style.transform='scale(1.1)'"
            onmouseout="this.style.transform='scale(1)'">
            <i class="ti ti-bookmark"></i>
        </a>

        <a href="#"
            data-size="lg"
            data-url="<?php echo e(route('leads.edit', $lead->id)); ?>"
            data-ajax-popup="true"
            data-bs-toggle="tooltip"
            title="<?php echo e(__('Edit')); ?>"
            style="display:inline-flex;align-items:center;justify-content:center;width:40px;height:40px;border-radius:50%;background:linear-gradient(to right, #065f46, #0f766e);color:white;box-shadow:0 4px 6px rgba(0,0,0,0.1);transition:transform 0.2s ease-in-out;"
            onmouseover="this.style.transform='scale(1.1)'"
            onmouseout="this.style.transform='scale(1)'">
            <i class="ti ti-pencil"></i>
        </a>

    </div>
<?php $__env->stopSection(); ?>

<?php $__env->startSection('content'); ?>
<div class="row">
    <div class="col-sm-12">
        <div class="row">

            <div class="col-xl-9 w-100">
                <?php
                $tasks = $lead->tasks;
                $products = $lead->products();
                $sources = $lead->sources();
                $calls = $lead->calls;
                $emails = $lead->emails;
                ?>
                <div id="general" class="row">
                    <!-- First row: Stage, Phone, Pipeline, Created -->
                    <div class="col-lg-3 col-md-6 col-12 mb-4">
                        <div class="card report-card h-100 mb-0 border-0 rounded-3"
                            style="box-shadow: 0 2px 10px rgba(0,128,0,0.15); transition: transform 0.3s ease, box-shadow 0.3s ease;"
                            onmouseover="this.style.transform='translateY(-4px)'; this.style.boxShadow='0 8px 20px rgba(0,128,0,0.35)'; this.querySelector('.report-icon').style.transform='rotate(8deg) scale(1.1)'; this.querySelector('i').style.transform='scale(1.4)';"
                            onmouseout="this.style.transform='translateY(0)'; this.style.boxShadow='0 2px 10px rgba(0,128,0,0.15)'; this.querySelector('.report-icon').style.transform='scale(1)'; this.querySelector('i').style.transform='scale(1.2)';">
                            <div class="card-body d-flex align-items-center gap-3 p-4">
                                <div class="report-icon d-flex align-items-center justify-content-center rounded-circle bg-primary bg-gradient shadow-lg"
                                    style="width: 48px; height: 48px; transition: transform 0.3s ease;">
                                    <i class="fa fa-layer-group text-white fs-5" style="transform: scale(1.2); transition: transform 0.3s ease;"></i>
                                </div>
                                <div class="report-info flex-1">
                                    <h6 class="mb-1 text-dark fw-semibold"><?php echo e(__('Stage')); ?></h6>
                                    <p class="text-muted mb-0 text-break">
                                        <?php echo e($lead->stage->name); ?>

                                    </p>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="col-lg-3 col-md-6 col-12 mb-4">
                        <div class="card report-card h-100 mb-0 border-0 rounded-3"
                            style="box-shadow: 0 2px 10px rgba(0,128,0,0.15); transition: transform 0.3s ease, box-shadow 0.3s ease;"
                            onmouseover="this.style.transform='translateY(-4px)'; this.style.boxShadow='0 8px 20px rgba(0,128,0,0.35)'; this.querySelector('.report-icon').style.transform='rotate(8deg) scale(1.1)'; this.querySelector('i').style.transform='scale(1.4)';"
                            onmouseout="this.style.transform='translateY(0)'; this.style.boxShadow='0 2px 10px rgba(0,128,0,0.15)'; this.querySelector('.report-icon').style.transform='scale(1)'; this.querySelector('i').style.transform='scale(1.2)';">
                            <div class="card-body d-flex align-items-center gap-3 p-4">
                                <div class="report-icon d-flex align-items-center justify-content-center rounded-circle bg-primary bg-gradient shadow-lg"
                                    style="width: 48px; height: 48px; transition: transform 0.3s ease;">
                                    <i class="ti ti-phone text-white fs-5" style="transform: scale(1.2); transition: transform 0.3s ease;"></i>
                                </div>
                                <div class="report-info flex-1">
                                    <h6 class="mb-1 text-dark fw-semibold"><?php echo e(__('Phone')); ?></h6>
                                    <p class="text-muted mb-0 text-break">
                                        <?php echo e(!empty($lead->phone) ? $lead->phone : 'Not Available'); ?>

                                    </p>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="col-lg-3 col-md-6 col-12 mb-4">
                        <div class="card report-card h-100 mb-0 border-0 rounded-3"
                            style="box-shadow: 0 2px 10px rgba(0,128,0,0.15); transition: transform 0.3s ease, box-shadow 0.3s ease;"
                            onmouseover="this.style.transform='translateY(-4px)'; this.style.boxShadow='0 8px 20px rgba(0,128,0,0.35)'; this.querySelector('.report-icon').style.transform='rotate(8deg) scale(1.1)'; this.querySelector('i').style.transform='scale(1.4)';"
                            onmouseout="this.style.transform='translateY(0)'; this.style.boxShadow='0 2px 10px rgba(0,128,0,0.15)'; this.querySelector('.report-icon').style.transform='scale(1)'; this.querySelector('i').style.transform='scale(1.2)';">
                            <div class="card-body d-flex align-items-center gap-3 p-4">
                                <div class="report-icon d-flex align-items-center justify-content-center rounded-circle bg-primary bg-gradient shadow-lg"
                                    style="width: 48px; height: 48px; transition: transform 0.3s ease;">
                                    <i class="fa fa-project-diagram text-white fs-5" style="transform: scale(1.2); transition: transform 0.3s ease;"></i>
                                </div>
                                <div class="report-info flex-1">
                                    <h6 class="mb-1 text-dark fw-semibold"><?php echo e(__('Pipeline')); ?></h6>
                                    <p class="text-muted mb-0 text-break">
                                        <?php echo e($lead->pipeline->name); ?>

                                    </p>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="col-lg-3 col-md-6 col-12 mb-4">
                        <div class="card report-card h-100 mb-0 border-0 rounded-3"
                            style="box-shadow: 0 2px 10px rgba(0,128,0,0.15); transition: transform 0.3s ease, box-shadow 0.3s ease;"
                            onmouseover="this.style.transform='translateY(-4px)'; this.style.boxShadow='0 8px 20px rgba(0,128,0,0.35)'; this.querySelector('.report-icon').style.transform='rotate(8deg) scale(1.1)'; this.querySelector('i').style.transform='scale(1.4)';"
                            onmouseout="this.style.transform='translateY(0)'; this.style.boxShadow='0 2px 10px rgba(0,128,0,0.15)'; this.querySelector('.report-icon').style.transform='scale(1)'; this.querySelector('i').style.transform='scale(1.2)';">
                            <div class="card-body d-flex align-items-center gap-3 p-4">
                                <div class="report-icon d-flex align-items-center justify-content-center rounded-circle bg-primary bg-gradient shadow-lg"
                                    style="width: 48px; height: 48px; transition: transform 0.3s ease;">
                                    <i class="ti ti-calendar text-white fs-5" style="transform: scale(1.2); transition: transform 0.3s ease;"></i>
                                </div>
                                <div class="report-info flex-1">
                                    <h6 class="mb-1 text-dark fw-semibold"><?php echo e(__('Created')); ?></h6>
                                    <p class="text-muted mb-0 text-break">
                                        <?php echo e(\Auth::user()->dateFormat($lead->created_at)); ?>

                                    </p>
                                </div>
                            </div>
                        </div>
                    </div>
                    <!-- Second row: Email, Source, (optionally) first custom field -->
                    <div class="col-lg-4 col-md-6 col-12 mb-4">
                        <div class="card report-card h-100 mb-0 border-0 rounded-3"
                            style="box-shadow: 0 2px 10px rgba(0,128,0,0.15); transition: transform 0.3s ease, box-shadow 0.3s ease;"
                            onmouseover="this.style.transform='translateY(-4px)'; this.style.boxShadow='0 8px 20px rgba(0,128,0,0.35)'; this.querySelector('.report-icon').style.transform='rotate(8deg) scale(1.1)'; this.querySelector('i').style.transform='scale(1.4)';"
                            onmouseout="this.style.transform='translateY(0)'; this.style.boxShadow='0 2px 10px rgba(0,128,0,0.15)'; this.querySelector('.report-icon').style.transform='scale(1)'; this.querySelector('i').style.transform='scale(1.2)';">
                            <div class="card-body d-flex align-items-center gap-3 p-4">
                                <div class="report-icon d-flex align-items-center justify-content-center rounded-circle bg-primary bg-gradient shadow-lg"
                                    style="width: 48px; height: 48px; transition: transform 0.3s ease;">
                                    <i class="ti ti-mail text-white fs-5" style="transform: scale(1.2); transition: transform 0.3s ease;"></i>
                                </div>
                                <div class="report-info flex-1">
                                    <h6 class="mb-1 text-dark fw-semibold"><?php echo e(__('Email')); ?></h6>
                                    <p class="text-muted mb-0 text-break">
                                        <?php echo e(!empty($lead->email) ? $lead->email : 'Not Available'); ?>

                                    </p>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="col-lg-4 col-md-6 col-12 mb-4">
                        <div class="card report-card h-100 mb-0 border-0 rounded-3"
                            style="box-shadow: 0 2px 10px rgba(0,128,0,0.15); transition: transform 0.3s ease, box-shadow 0.3s ease;"
                            onmouseover="this.style.transform='translateY(-4px)'; this.style.boxShadow='0 8px 20px rgba(0,128,0,0.35)'; this.querySelector('.report-icon').style.transform='rotate(8deg) scale(1.1)'; this.querySelector('i').style.transform='scale(1.4)';"
                            onmouseout="this.style.transform='translateY(0)'; this.style.boxShadow='0 2px 10px rgba(0,128,0,0.15)'; this.querySelector('.report-icon').style.transform='scale(1)'; this.querySelector('i').style.transform='scale(1.2)';">
                            <div class="card-body d-flex align-items-center gap-3 p-4">
                                <div class="report-icon d-flex align-items-center justify-content-center rounded-circle bg-primary bg-gradient shadow-lg"
                                    style="width: 48px; height: 48px; transition: transform 0.3s ease;">
                                    <i class="fas fa-sitemap text-white fs-5" style="transform: scale(1.2); transition: transform 0.3s ease;"></i>
                                </div>
                                <div class="report-info flex-1">
                                    <h6 class="mb-1 text-dark fw-semibold"><?php echo e(__('Source')); ?></h6>
                                    <p class="text-muted mb-0 text-break">
                                        <?php if(count($sources) > 0): ?>
                                        <?php echo e(collect($sources)->pluck('name')->implode(', ')); ?>

                                        <?php else: ?>
                                        -
                                        <?php endif; ?>
                                    </p>
                                </div>
                            </div>
                        </div>
                    </div>
                    <?php if(!$customFields->isEmpty()): ?>
                    <?php
                    $leadCustomValues = \App\Models\CustomField::getData($lead, 'Leads');
                    $firstField = $customFields->first();
                    $value = $leadCustomValues[$firstField->id] ?? null;
                    ?>
                    <div class="col-lg-4 col-md-6 col-12 mb-4">
                        <div class="card report-card h-100 mb-0 border-0 rounded-3"
                            style="box-shadow: 0 2px 10px rgba(0,128,0,0.15); transition: transform 0.3s ease, box-shadow 0.3s ease;"
                            onmouseover="this.style.transform='translateY(-4px)'; this.style.boxShadow='0 8px 20px rgba(0,128,0,0.35)'; this.querySelector('.report-icon').style.transform='rotate(8deg) scale(1.1)'; this.querySelector('i').style.transform='scale(1.4)';"
                            onmouseout="this.style.transform='translateY(0)'; this.style.boxShadow='0 2px 10px rgba(0,128,0,0.15)'; this.querySelector('.report-icon').style.transform='scale(1)'; this.querySelector('i').style.transform='scale(1.2)';">
                            <div class="card-body d-flex align-items-center gap-3 p-4">
                                <div class="report-icon d-flex align-items-center justify-content-center rounded-circle bg-primary bg-gradient shadow-lg"
                                    style="width: 48px; height: 48px; transition: transform 0.3s ease;">
                                    <i class="fas fa-users-cog text-white fs-5" style="transform: scale(1.2); transition: transform 0.3s ease;"></i>
                                </div>
                                <div class="report-info flex-1">
                                    <h6 class="mb-1 text-dark fw-semibold"><?php echo e($firstField->name); ?></h6>
                                    <p class="text-muted text-break mb-0">
                                        <?php if(is_array($value)): ?>
                                        <?php echo e(implode(', ', $value)); ?>

                                        <?php elseif(!is_null($value)): ?>
                                        <?php echo e($value); ?>

                                        <?php else: ?>
                                        <?php echo e(__('Not set')); ?>

                                        <?php endif; ?>
                                    </p>
                                </div>
                            </div>
                        </div>
                    </div>
                    <?php endif; ?>
                    <!-- Additional custom fields (if any) -->
                    <?php if(!$customFields->isEmpty() && $customFields->count() > 1): ?>
                    <div id="custom_fields" class="row mt-4">
                        <div class="col-12">
                            <h4 class="mb-3"><?php echo e(__('Additional Information')); ?></h4>
                        </div>
                        <?php $__currentLoopData = $customFields->slice(1); $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $field): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                        <div class="col-md-4 col-sm-6 col-12 mb-4">
                            <div class="card report-card h-100 mb-0 border-0 rounded-3"
                                style="box-shadow: 0 2px 10px rgba(0,128,0,0.15); transition: transform 0.3s ease, box-shadow 0.3s ease;"
                                onmouseover="this.style.transform='translateY(-4px)'; this.style.boxShadow='0 8px 20px rgba(0,128,0,0.35)'; this.querySelector('.report-icon').style.transform='rotate(8deg) scale(1.1)'; this.querySelector('i').style.transform='scale(1.4)';"
                                onmouseout="this.style.transform='translateY(0)'; this.style.boxShadow='0 2px 10px rgba(0,128,0,0.15)'; this.querySelector('.report-icon').style.transform='scale(1)'; this.querySelector('i').style.transform='scale(1.2)';">
                                <div class="card-body d-flex align-items-center gap-3 p-4">
                                    <div class="report-icon d-flex align-items-center justify-content-center rounded-circle bg-primary bg-gradient shadow-lg"
                                        style="width: 48px; height: 48px; transition: transform 0.3s ease;">
                                        <i class="fas fa-users-cog text-white fs-5" style="transform: scale(1.2); transition: transform 0.3s ease;"></i>
                                    </div>
                                    <div class="report-info flex-1">
                                        <h6 class="mb-1 text-dark fw-semibold"><?php echo e($field->name); ?></h6>
                                        <p class="text-muted text-break mb-0">
                                            <?php
                                            $value = $leadCustomValues[$field->id] ?? null;
                                            ?>
                                            <?php if(is_array($value)): ?>
                                            <?php echo e(implode(', ', $value)); ?>

                                            <?php elseif(!is_null($value)): ?>
                                            <?php echo e($value); ?>

                                            <?php else: ?>
                                            <?php echo e(__('Not set')); ?>

                                            <?php endif; ?>
                                        </p>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                    </div>
                    <?php endif; ?>
                </div>
                <!-- Horizontal Tab Menu -->
                <div class="row mt-4">
                    <div class="col-12">
                        <div class="d-flex flex-wrap justify-content-center gap-2 tab-menu-responsive ">
                            <button class="tab-btn active bg-primary bg-gradient" data-tab="followups"><?php echo e(__('Follow-Ups')); ?></button>
                            <button class="tab-btn bg-primary bg-gradient" data-tab="usersproducts"><?php echo e(__('Users')); ?></button>
                            <button class="tab-btn bg-primary bg-gradient" data-tab="notesfiles"><?php echo e(__('Notes & Files')); ?></button>
                            <button class="tab-btn bg-primary bg-gradient" data-tab="activity"><?php echo e(__('Activity')); ?></button>
                            <button class="tab-btn bg-primary bg-gradient" data-tab="comments"><?php echo e(__('Comments')); ?></button>
                        </div>
                    </div>
                </div>
                <!-- Tab Content Sections -->
                <div class="tab-section mt-4" id="tab-followups">
                    <!-- Follow-Ups section (moved from #tasks) -->
                    <div class="card mb-2">
                        <div class="card-header">
                            <div class="d-flex align-items-center justify-content-between">
                                <h5><?php echo e(__('Follow-Ups')); ?></h5>
                                <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('create lead task')): ?>
                                <div class="float-end">
                                    <a href="#" data-size="lg" data-url="<?php echo e(route('leads.tasks.create', $lead->id)); ?>" data-ajax-popup="true" data-bs-toggle="tooltip" title="<?php echo e(__('Follow-Ups')); ?>" class="btn btn-sm btn-primary">
                                        <i class="ti ti-plus"></i>
                                    </a>
                                </div>
                                <?php endif; ?>
                            </div>
                        </div>
                        <div class="card-body">
                            <div class="table-responsive">
                                <table class="table table-hover mb-0">
                                    <thead>
                                        <tr>
                                            <th><?php echo e(__('Name')); ?></th>
                                            <th><?php echo e(__('Status')); ?></th>
                                            <th><?php echo e(__('Due Date')); ?></th>
                                            <th><?php echo e(__('Assigned to')); ?></th>
                                            <th><?php echo e(__('Action')); ?></th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <?php $__currentLoopData = $lead->tasks; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $task): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                        <tr>
                                            <td>
                                                <a href="#" class="lead-task-details-link text-primary" data-url="<?php echo e(route('leads.tasks.show', [$lead->id, $task->id])); ?>" style="text-decoration:underline;cursor:pointer;"><?php echo e($task->name); ?></a>
                                            </td>
                                            <td>
                                                <?php if(isset($task->status) && isset(\App\Models\LeadTask::$status[$task->status])): ?>
                                                    <div class="badge bg-success p-2 px-3 rounded"><?php echo e(__(\App\Models\LeadTask::$status[$task->status])); ?></div>
                                                <?php else: ?>
                                                    <div class="badge bg-warning p-2 px-3 rounded"><?php echo e(__('Unknown')); ?></div>
                                                <?php endif; ?>
                                            </td>
                                            <td><?php echo e(Auth::user()->dateFormat($task->date)); ?></td>
                                            <td><?php echo e($task->assignedUser ? $task->assignedUser->name : ''); ?></td>
                                            <td>
                                                <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('edit lead task')): ?>
                                                <div class="action-btn bg-info ms-2">
                                                    <a href="#" class="mx-3 btn btn-sm d-inline-flex align-items-center" data-url="<?php echo e(route('leads.tasks.edit', [$lead->id, $task->id])); ?>" data-ajax-popup="true" data-size="lg" data-bs-toggle="tooltip" title="<?php echo e(__('Edit')); ?>" data-title="<?php echo e(__('Edit Task')); ?>">
                                                        <i class="ti ti-pencil text-white"></i>
                                                    </a>
                                                </div>
                                                <?php endif; ?>
                                                <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('delete lead task')): ?>
                                                <div class="action-btn bg-danger ms-2">
                                                    <?php echo Form::open(['method' => 'DELETE', 'route' => ['leads.tasks.destroy', $lead->id, $task->id], 'id' => 'delete-form-'.$task->id]); ?>

                                                    <a href="#" class="mx-3 btn btn-sm  align-items-center bs-pass-para" data-bs-toggle="tooltip" title="<?php echo e(__('Delete')); ?>">
                                                        <i class="ti ti-trash text-white"></i>
                                                    </a>
                                                    <?php echo Form::close(); ?>

                                                </div>
                                                <?php endif; ?>
                                            </td>
                                        </tr>
                                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="tab-section mt-4 d-none" id="tab-usersproducts">
                    <!-- Users & Products section (moved from #users_products) -->
                    <div class="row">
                        <div class="col-12 mb-4">
                            <div class="card h-100 mb-0">
                                <div class="card-header">
                                    <div class="d-flex align-items-center justify-content-between">
                                        <h5><?php echo e(__('Users')); ?></h5>
                                        <div class="float-end">
                                            <a href="#"
                                                data-size="md"
                                                data-url="<?php echo e(route('leads.users.edit', $lead->id)); ?>"
                                                data-ajax-popup="true"
                                                data-bs-toggle="tooltip"
                                                title="<?php echo e(__('Add User')); ?>"
                                                style="display:inline-flex;align-items:center;justify-content:center;width:40px;height:40px;border-radius:50%;background:linear-gradient(to right, #065f46, #0f766e);color:white;box-shadow:0 4px 6px rgba(0,0,0,0.1);transition:transform 0.2s ease-in-out;"
                                                onmouseover="this.style.transform='scale(1.1)'"
                                                onmouseout="this.style.transform='scale(1)'">
                                                <i class="ti ti-plus"></i>
                                            </a>
                                        </div>
                                    </div>
                                </div>
                                <div class="card-body">
                                    <div class="table-responsive">
                                        <table class="table table-hover mb-0">
                                            <thead>
                                                <tr>
                                                    <th><?php echo e(__('Name')); ?></th>
                                                    <th><?php echo e(__('Action')); ?></th>
                                                </tr>
                                            </thead>
                                            <tbody>
                                                <?php $__currentLoopData = $lead->users; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $user): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                                <tr>
                                                    <td>
                                                        <div class="d-flex align-items-center">
                                                            <div>
                                                                <img <?php if($user->avatar): ?> src="<?php echo e(asset('/storage/uploads/avatar/' . $user->avatar)); ?>" <?php else: ?> src="<?php echo e(asset('/storage/uploads/avatar/avatar.png')); ?>" <?php endif; ?>
                                                                class=" rounded border-2 border border-primary wid-40 me-3"
                                                                alt="avatar image">
                                                            </div>
                                                            <p class="mb-0"><?php echo e($user->name); ?></p>
                                                        </div>
                                                    </td>
                                                    <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('edit lead')): ?>
                                                    <td>
                                                        <div class="action-btn me-2">
                                                            <?php echo Form::open([
                                                            'method' => 'DELETE',
                                                            'route' => ['leads.users.destroy', $lead->id, $user->id],
                                                            'id' => 'delete-form-' . $lead->id,
                                                            ]); ?>

                                                            <a href="#"
                                                                class="mx-3 btn btn-sm  align-items-center bs-pass-para bg-danger"
                                                                data-bs-toggle="tooltip"
                                                                title="<?php echo e(__('Delete')); ?>"><i
                                                                    class="ti ti-trash text-white"></i></a>

                                                            <?php echo Form::close(); ?>

                                                        </div>
                                                    </td>
                                                    <?php endif; ?>
                                                </tr>
                                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                            </tbody>
                                        </table>
                                    </div>

                                </div>
                            </div>
                        </div>
                        <!-- <div class="col-md-6 col-12 mb-4">
                            <div class="card h-100 mb-0">
                                <div class="card-header">
                                    <div class="d-flex align-items-center justify-content-between">
                                        <h5><?php echo e(__('Products')); ?></h5>
                                        <div class="float-end">
                                            <a href="#"
                                                data-size="md"
                                                data-url="<?php echo e(route('leads.products.edit', $lead->id)); ?>"
                                                data-ajax-popup="true"
                                                data-bs-toggle="tooltip"
                                                title="<?php echo e(__('Add Product')); ?>"
                                                style="display:inline-flex;align-items:center;justify-content:center;width:40px;height:40px;border-radius:50%;background:linear-gradient(to right, #065f46, #0f766e);color:white;box-shadow:0 4px 6px rgba(0,0,0,0.1);transition:transform 0.2s ease-in-out;"
                                                onmouseover="this.style.transform='scale(1.1)'"
                                                onmouseout="this.style.transform='scale(1)'">
                                                <i class="ti ti-plus"></i>
                                            </a>
                                        </div>
                                    </div>
                                </div>
                                <div class="card-body">
                                    <div class="table-responsive">
                                        <table class="table table-hover mb-0">
                                            <thead>
                                                <tr>
                                                    <th><?php echo e(__('Name')); ?></th>
                                                    <th><?php echo e(__('Price')); ?></th>
                                                    <th><?php echo e(__('Action')); ?></th>
                                                </tr>
                                            </thead>
                                            <tbody>
                                                <?php $__currentLoopData = $lead->products(); $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $product): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                                <tr>
                                                    <td>
                                                        <?php echo e($product->name); ?>

                                                    </td>
                                                    <td>
                                                        <?php echo e(\Auth::user()->priceFormat($product->sale_price)); ?>

                                                    </td>
                                                    <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('edit lead')): ?>
                                                    <td>
                                                        <div class="action-btn me-2">
                                                            <?php echo Form::open(['method' => 'DELETE', 'route' => ['leads.products.destroy', $lead->id, $product->id]]); ?>

                                                            <a href="#"
                                                                class="mx-3 btn btn-sm  align-items-center bs-pass-para bg-danger"
                                                                data-bs-toggle="tooltip"
                                                                title="<?php echo e(__('Delete')); ?>"><i
                                                                    class="ti ti-trash text-white"></i></a>

                                                            <?php echo Form::close(); ?>

                                                        </div>
                                                    </td>
                                                    <?php endif; ?>
                                                </tr>
                                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                            </tbody>
                                        </table>
                                    </div>

                                </div>
                            </div>
                        </div> -->
                    </div>
                </div>
                <div class="tab-section mt-4 d-none" id="tab-notesfiles">
                    <!-- Notes & Files section (moved from #discussion_note) -->
                    <div class="row">
                        <div class="col-md-6 col-12 mb-4">
                            <!-- Notes Section -->
                            <div class="card h-100 mb-0">
                                <div class="card-header">
                                    <div class="d-flex flex-wrap align-items-center justify-content-between gap-3">
                                        <h5><?php echo e(__('Notes')); ?></h5>
                                        <?php
                                        $user = \App\Models\User::find(\Auth::user()->creatorId());
                                        $plan = \App\Models\Plan::getPlan($user->plan);
                                        ?>
                                        <?php if($plan && $plan->chatgpt == 1): ?>
                                        <div class="float-end d-flex flex-wrap align-items-center gap-2">
                                            <a href="#" data-size="md"
                                                class="btn btn-primary btn-icon btn-sm m-0"
                                                data-ajax-popup-over="true" id="grammarCheck"
                                                data-url="<?php echo e(route('grammar', ['grammar'])); ?>"
                                                data-bs-placement="top"
                                                data-title="<?php echo e(__('Grammar check with AI')); ?>">
                                                <i class="ti ti-rotate"></i>
                                                <span><?php echo e(__('Grammar check with AI')); ?></span>
                                            </a>
                                            <a href="#" data-size="md"
                                                class="btn btn-primary btn-icon btn-sm m-0"
                                                data-ajax-popup-over="true"
                                                data-url="<?php echo e(route('generate', ['lead'])); ?>"
                                                data-bs-placement="top"
                                                data-title="<?php echo e(__('Generate content with AI')); ?>">
                                                <i class="fas fa-robot"></i>
                                                <span><?php echo e(__('Generate with AI')); ?></span>
                                            </a>
                                        </div>
                                        <?php endif; ?>
                                    </div>
                                </div>
                                <div class="card-body">
                                    <textarea class="summernote-simple" name="note"><?php echo $lead->notes; ?></textarea>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6 col-12 mb-4">
                            <!-- Files Section -->
                            <div class="card h-100 mb-0">
                                <div class="card-header">
                                    <h5><?php echo e(__('Files')); ?></h5>
                                </div>
                                <div class="card-body">
                                    <div class="col-md-12 dropzone top-5-scroll browse-file" id="dropzonewidget" style="max-height: 150px; overflow-y: auto;">
                                        <div class="text-center text-muted py-4" style="pointer-events: none;">
                                            <i class="ti ti-upload" style="font-size: 2rem;"></i>
                                            <div><?php echo e(__('Upload file or drag file to upload here')); ?></div>
                                        </div>
                                    </div>
                                    <style>
                                        /* Hide Dropzone file previews */
                                        #dropzonewidget .dz-preview {
                                            display: none !important;
                                        }
                                    </style>
                                </div>
                            </div>
                        </div>
                        <div class="col-12 mb-4">
                            <!-- Uploaded Files List Section -->
                            <div class="card h-100 mb-0">
                                <div class="card-header">
                                    <h5><?php echo e(__('Uploaded Files')); ?></h5>
                                </div>
                                <div class="card-body">
                                    <div class="table-responsive" style="max-height: 250px; overflow-y: auto;">
                                        <table class="table table-hover align-middle mb-0" id="uploaded-files-table">
                                            <thead>
                                                <tr>
                                                    <th style="width:40px;"></th>
                                                    <th><?php echo e(__('File Name')); ?></th>
                                                    <th><?php echo e(__('Uploaded At')); ?></th>
                                                    <th><?php echo e(__('Action')); ?></th>
                                                </tr>
                                            </thead>
                                            <tbody>
                                                <?php $__empty_1 = true; $__currentLoopData = $lead->files; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $file): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); $__empty_1 = false; ?>
                                                <?php if(\Illuminate\Support\Facades\Storage::disk('public')->exists($file->file_path)): ?>
                                                <tr>
                                                    <td>
                                                        <i class="ti <?php echo e($file->icon); ?> fs-4 text-primary"></i>
                                                    </td>
                                                    <td class="text-break">
                                                        <?php echo e($file->original_name ?? $file->file_name); ?>

                                                        <?php if($file->file_size): ?>
                                                            <br><small class="text-muted"><?php echo e($file->formatted_size); ?></small>
                                                        <?php endif; ?>
                                                    </td>
                                                    <td>
                                                        <small class="text-muted"><?php echo e($file->created_at ? $file->created_at->diffForHumans() : '-'); ?></small>
                                                        <?php if($file->uploader): ?>
                                                            <br><small class="text-muted">by <?php echo e($file->uploader->name); ?></small>
                                                        <?php endif; ?>
                                                    </td>
                                                    <td>
                                                        <a href="<?php echo e(route('leads.file.download', [$lead->id, $file->id])); ?>" class="btn btn-sm btn-outline-info me-1" title="<?php echo e(__('Download')); ?>">
                                                            <i class="ti ti-download"></i>
                                                        </a>
                                                        <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('edit lead')): ?>
                                                        <button type="button" class="btn btn-sm btn-outline-danger delete-file-btn"
                                                            data-file-id="<?php echo e($file->id); ?>"
                                                            data-delete-url="<?php echo e(route('leads.file.delete', [$lead->id, $file->id])); ?>"
                                                            title="<?php echo e(__('Delete')); ?>">
                                                            <i class="ti ti-trash"></i>
                                                        </button>
                                                        <?php endif; ?>
                                                    </td>
                                                </tr>
                                                <?php endif; ?>
                                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); if ($__empty_1): ?>
                                                <tr>
                                                    <td colspan="4" class="text-center text-muted"><?php echo e(__('No files uploaded yet.')); ?></td>
                                                </tr>
                                                <?php endif; ?>
                                            </tbody>
                                        </table>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="tab-section mt-4 d-none" id="tab-activity">
                    <!-- Activity section (moved from #activity) -->
                    <div class="card">
                        <div class="card-header">
                            <h5><?php echo e(__('Activity')); ?></h5>
                        </div>
                        <div class="card-body ">
                            <div class="row leads-scroll">
                                <ul class="event-cards list-group list-group-flush mt-3 w-100" style="max-height: 250px; overflow-y: auto;">
                                    <?php if(!$lead->activities->isEmpty()): ?>
                                    <?php $__currentLoopData = $lead->activities; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $activity): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                    <li class="list-group-item card mb-3">
                                        <div class="row align-items-center justify-content-between">
                                            <div class="col-auto mb-3 mb-sm-0">
                                                <div class="d-flex align-items-center">
                                                    <div class="theme-avtar bg-primary badge">
                                                        <i class="ti <?php echo e($activity->logIcon()); ?>"></i>
                                                    </div>
                                                    <div class="ms-3">
                                                        <span
                                                            class="text-dark text-sm"><?php echo e(__($activity->log_type)); ?></span>
                                                        <h6 class="m-0"><?php echo $activity->getLeadRemark(); ?></h6>
                                                        <small
                                                            class="text-muted"><?php echo e($activity->created_at->diffForHumans()); ?></small>
                                                    </div>
                                                </div>
                                            </div>
                                            <div class="col-auto">

                                            </div>
                                        </div>
                                    </li>
                                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                    <?php else: ?>
                                    No activity found yet.
                                    <?php endif; ?>
                                </ul>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="tab-section mt-4 d-none" id="tab-comments">
                    <div class="row mt-4">
                        <div class="col-12">
                            <div class="card">
                                <div class="card-body">
                                    <div class="comments-youtube-ui bg-white text-dark p-3 rounded shadow-sm">
                                        <!-- Total Comments Count -->
                                        <?php
                                            $totalComments = $lead->comments->where('parent_id', null)->count();
                                        ?>
                                        <div class="d-flex align-items-center mb-3">
                                            <h5 class="mb-0">
                                                <i class="ti ti-message-circle me-1"></i>
                                                <?php echo e(__('Comments')); ?>

                                                <span class="badge bg-primary align-middle ms-2" style="font-size:1rem;"><?php echo e($totalComments); ?></span>
                                            </h5>
                                        </div>
                                        <!-- Comment Input -->
                                        <div class="d-flex align-items-start mb-4">
                                            <div class="flex-shrink-0">
                                                <div class="theme-avtar bg-primary text-white d-flex align-items-center justify-content-center" style="width: 40px; height: 40px; font-size: 1.2rem; border-radius: 50%;">
                                                    <?php echo e(substr(Auth::user()->name ?? 'U', 0, 1)); ?>

                                                </div>
                                            </div>
                                            <div class="flex-grow-1 ms-3">
                                                <form id="commentForm" action="<?php echo e(route('leads.comments.store', $lead->id)); ?>" method="POST" enctype="multipart/form-data" class="w-100">
                                                    <?php echo csrf_field(); ?>
                                                    <div class="mb-2">
                                                        <textarea name="comment" id="comment" class="form-control bg-white text-dark border-secondary" rows="2" placeholder="Add a comment..." required style="resize:none;"></textarea>
                                                    </div>
                                                    <div class="d-flex align-items-center gap-2">
                                                        <button type="submit" class="btn btn-primary btn-sm ms-auto px-4">Comment</button>
                                                    </div>
                                                </form>
                                            </div>
                                        </div>
                                        <!-- Comments List -->
                                        <div class="comments-list">
                                            <?php if(!empty($lead->comments) && count($lead->comments) > 0): ?>
                                            <?php $__currentLoopData = $lead->comments->where('parent_id', null)->sortByDesc('created_at'); $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $comment): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                            <div class="comment-item py-3 border-bottom border-secondary bg-white">
                                                <div class="d-flex align-items-start">
                                                    <div class="flex-shrink-0">
                                                        <div class="theme-avtar bg-primary text-white d-flex align-items-center justify-content-center" style="width: 36px; height: 36px; font-size: 1.1rem; border-radius: 50%;">
                                                            <?php echo e(substr($comment->user->name ?? 'U', 0, 1)); ?>

                                                        </div>
                                                    </div>
                                                    <div class="flex-grow-1 ms-3">
                                                        <div class="d-flex align-items-center gap-2 mb-1">
                                                            <span class="fw-semibold"><?php echo e($comment->user->name ?? __('Unknown User')); ?></span>
                                                            <span class="text-muted small"><?php echo e($comment->created_at->diffForHumans()); ?></span>
                                                        </div>
                                                        <div class="mb-2"><?php echo preg_replace('/@(\w+)/', '<span class="mentioned-user">@$1</span>', $comment->comment); ?></div>
                                                        <?php if(isset($comment->files) && count($comment->files) > 0): ?>
                                                        <div class="comment-files mb-2">
                                                            <small class="text-muted d-block mb-1"><?php echo e(__('Attachments')); ?>:</small>
                                                            <div class="d-flex flex-wrap gap-1">
                                                                <?php $__currentLoopData = $comment->files; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $file): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                                                <a href="#" class="badge bg-info text-decoration-none" title="<?php echo e($file->original_name ?? $file->name); ?>">
                                                                    <i class="ti ti-paperclip"></i> <?php echo e(Str::limit($file->original_name ?? $file->name, 15)); ?>

                                                                </a>
                                                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                                            </div>
                                                        </div>
                                                        <?php endif; ?>
                                                        <div class="d-flex align-items-center gap-2 mt-1">
                                                            <button type="button" class="btn btn-sm btn-outline-secondary px-2 py-1 reaction-btn" data-comment-id="<?php echo e($comment->id); ?>" data-reaction="like"><i class="ti ti-thumb-up"></i></button>
                                                            <button type="button" class="btn btn-sm btn-outline-secondary px-2 py-1 reaction-btn" data-comment-id="<?php echo e($comment->id); ?>" data-reaction="love"><i class="ti ti-heart"></i></button>
                                                            <button type="button" class="btn btn-sm btn-outline-secondary px-2 py-1 reply-btn" data-comment-id="<?php echo e($comment->id); ?>" data-user-name="<?php echo e($comment->user->name); ?>"><i class="ti ti-message-reply"></i> Reply</button>
                                                            <div class="dropdown ms-auto">
                                                                <button class="btn btn-sm btn-outline-secondary px-2 py-1 dropdown-toggle" type="button" data-bs-toggle="dropdown"><i class="ti ti-dots-vertical"></i></button>
                                                                <ul class="dropdown-menu">
                                                                    <li><a class="dropdown-item text-danger" href="#" onclick="deleteComment(<?php echo e($comment->id); ?>)"><i class="ti ti-trash"></i> <?php echo e(__('Delete')); ?></a></li>
                                                                </ul>
                                                            </div>
                                                        </div>
                                                        <div class="reaction-counts mt-1" id="reaction-counts-<?php echo e($comment->id); ?>">
                                                            <?php
                                                            $reactions = $comment->comment_reaction ?? [];
                                                            $counts = [];
                                                            foreach ($reactions as $reaction_data) {
                                                            $reaction = $reaction_data['reaction'];
                                                            if (!isset($counts[$reaction])) {
                                                            $counts[$reaction] = 0;
                                                            }
                                                            $counts[$reaction]++;
                                                            }
                                                            ?>
                                                            <?php if(!empty($counts)): ?>
                                                            <?php $__currentLoopData = $counts; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $reaction => $count): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                                            <span class="badge bg-light text-dark me-1">
                                                                <?php if($reaction == 'like'): ?> 👍 <?php elseif($reaction == 'love'): ?> ❤️ <?php endif; ?>
                                                                <?php echo e($count); ?>

                                                            </span>
                                                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                                            <?php endif; ?>
                                                        </div>
                                                        <!-- Reply Form (hidden by default) -->
                                                        <div class="reply-form mt-3" id="reply-form-<?php echo e($comment->id); ?>" style="display: none;">
                                                            <div class="d-flex align-items-start">
                                                                <div class="theme-avtar bg-secondary text-white d-flex align-items-center justify-content-center" style="width: 30px; height: 30px; font-size: 1rem; border-radius: 50%;">
                                                                    <?php echo e(substr(Auth::user()->name ?? 'U', 0, 1)); ?>

                                                                </div>
                                                                <div class="flex-grow-1 ms-2">
                                                                    <textarea class="form-control reply-textarea bg-white text-dark border-secondary" rows="2" placeholder="Reply..." data-comment-id="<?php echo e($comment->id); ?>"></textarea>
                                                                    <div class="d-flex justify-content-end gap-2 mt-2">
                                                                        <button type="button" class="btn btn-sm btn-secondary cancel-reply" data-comment-id="<?php echo e($comment->id); ?>"><?php echo e(__('Cancel')); ?></button>
                                                                        <button type="button" class="btn btn-sm btn-primary submit-reply" data-comment-id="<?php echo e($comment->id); ?>"><?php echo e(__('Reply')); ?></button>
                                                                    </div>
                                                                </div>
                                                            </div>
                                                        </div>
                                                        <!-- Replies -->
                                                        <div class="replies mt-3 ms-4" id="replies-<?php echo e($comment->id); ?>">
                                                            <?php
                                                            $replies = $lead->comments->where('parent_id', $comment->id)->sortBy('created_at');
                                                            ?>
                                                            <?php if($replies && count($replies) > 0): ?>
                                                            <?php $__currentLoopData = $replies; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $reply): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                                            <div class="reply-item d-flex align-items-start mb-2 bg-light">
                                                                <div class="theme-avtar bg-secondary text-white d-flex align-items-center justify-content-center" style="width: 30px; height: 30px; font-size: 1rem; border-radius: 50%;">
                                                                    <?php echo e(substr($reply->user->name ?? 'U', 0, 1)); ?>

                                                                </div>
                                                                <div class="flex-grow-1 ms-2">
                                                                    <div class="d-flex align-items-center gap-2 mb-1">
                                                                        <span class="fw-semibold small"><?php echo e($reply->user->name ?? __('Unknown User')); ?></span>
                                                                        <span class="text-muted small"><?php echo e($reply->created_at->diffForHumans()); ?></span>
                                                                    </div>
                                                                    <div class="mb-1 small"><?php echo preg_replace('/@(\w+)/', '<span class="mentioned-user">@$1</span>', $reply->comment); ?></div>
                                                                    <div class="d-flex align-items-center gap-2 mt-1">
                                                                        <!-- <button type="button" class="btn btn-sm btn-outline-secondary px-2 py-1 reply-btn" data-comment-id="<?php echo e($reply->id); ?>" data-user-name="<?php echo e($reply->user->name); ?>"><i class="ti ti-message-reply"></i> Reply</button> -->
                                                                        <div class="dropdown ms-auto">
                                                                            <button class="btn btn-sm btn-outline-secondary px-2 py-1 dropdown-toggle" type="button" data-bs-toggle="dropdown"><i class="ti ti-dots-vertical"></i></button>
                                                                            <ul class="dropdown-menu">
                                                                                <li><a class="dropdown-item text-danger" href="#" onclick="deleteComment(<?php echo e($reply->id); ?>)"><i class="ti ti-trash"></i> <?php echo e(__('Delete')); ?></a></li>
                                                                            </ul>
                                                                        </div>
                                                                    </div>
                                                                </div>
                                                            </div>
                                                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                                            <?php endif; ?>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                            <?php else: ?>
                                            <div class="text-center py-4">
                                                <i class="ti ti-message-circle text-muted" style="font-size: 3rem;"></i>
                                                <p class="text-muted mt-2"><?php echo e(__('No comments yet')); ?></p>
                                            </div>
                                            <?php endif; ?>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
</div>

<div class="modal fade" id="addCommentModal" tabindex="-1" aria-labelledby="addCommentModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <form action="<?php echo e(route('leads.comments.store', $lead->id)); ?>" method="POST" enctype="multipart/form-data">
                <?php echo csrf_field(); ?>
                <div class="modal-header">
                    <h5 class="modal-title" id="addCommentModalLabel"><?php echo e(__('Add Comment')); ?></h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <div class="form-group">
                        <label for="comment" class="form-label"><?php echo e(__('Comment')); ?></label>
                        <textarea name="comment" id="comment" class="form-control mention-textarea" rows="4" placeholder="<?php echo e(__('Enter your comment... Type @ to mention users')); ?>" required></textarea>
                        <small class="text-muted"><?php echo e(__('Type @ followed by a name to mention users')); ?></small>
                    </div>

                    <div class="form-group mt-3">
                        <label for="comment_files" class="form-label"><?php echo e(__('Attach Files (Optional)')); ?></label>
                        <input type="file" name="comment_files[]" id="comment_files" class="form-control" multiple accept=".jpg,.jpeg,.png,.pdf,.doc,.docx,.txt">
                        <small class="text-muted"><?php echo e(__('You can upload multiple files (Images, PDFs, Documents)')); ?></small>
                    </div>

                    <div id="mention-dropdown" class="mention-dropdown" style="display: none;">
                        <ul class="list-group" id="mention-list"></ul>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal"><?php echo e(__('Cancel')); ?></button>
                    <button type="submit" class="btn btn-primary"><?php echo e(__('Add Comment')); ?></button>
                </div>
            </form>
        </div>
    </div>
</div>


<div class="modal fade" id="deleteFileModal" tabindex="-1" aria-labelledby="deleteFileModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-dialog-centered">
        <div class="modal-content">
            <div class="modal-header border-0 pb-0">
                <h5 class="modal-title" id="deleteFileModalLabel">
                    <i class="ti ti-alert-triangle text-warning me-2"></i>
                    <?php echo e(__('Delete File')); ?>

                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body pt-0">
                <div class="text-center mb-3">
                    <div class="avatar avatar-lg bg-danger-light rounded-circle mx-auto mb-3">
                        <i class="ti ti-trash fs-2 text-danger"></i>
                    </div>
                    <h6 class="mb-2"><?php echo e(__('Are you sure?')); ?></h6>
                    <p class="text-muted mb-0">
                        <?php echo e(__('You are about to delete')); ?> "<span id="deleteFileName" class="fw-bold"></span>".
                        <br><?php echo e(__('This action cannot be undone.')); ?>

                    </p>
                </div>
            </div>
            <div class="modal-footer border-0 pt-0">
                <button type="button" class="btn btn-light" data-bs-dismiss="modal">
                    <i class="ti ti-x me-1"></i><?php echo e(__('Cancel')); ?>

                </button>
                <button type="button" class="btn btn-danger" id="confirmDeleteBtn">
                    <i class="ti ti-trash me-1"></i><?php echo e(__('Delete')); ?>

                </button>
            </div>
        </div>
    </div>
</div>


<div class="modal fade" id="leadTaskDetailsModal" tabindex="-1" aria-labelledby="leadTaskDetailsModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg modal-dialog-centered">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="leadTaskDetailsModalLabel"><?php echo e(__('Follow Up Details')); ?></h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <!-- Content loaded via AJAX -->
            </div>
        </div>
    </div>
</div>
<?php $__env->startPush('script-page'); ?>
<script>
$(document).ready(function() {
    // Get real users for mentions
    let users = <?php echo json_encode($mentionUsers ?? [], 15, 512) ?>;

    // Reaction functionality
    $(document).on('click', '.reaction-btn', function() {
        let btn = $(this);
        let commentId = btn.data('comment-id');
        let reaction = btn.data('reaction');
        
        $.ajax({
            url: `/leads/comments/${commentId}/react`,
            method: 'POST',
            data: { reaction: reaction },
            headers: {
                'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content'),
            },
            success: function(response) {
                if (response.success) {
                    updateReactionCounts(commentId, response.reactions);
                    btn.removeClass('btn-outline-primary btn-outline-danger').addClass('btn-' + getReactionColor(reaction));
                }
            },
            error: function(xhr, status, error) {
                console.error('Reaction error:', xhr.responseText);
                alert('Error updating reaction');
            }
        });
    });

    // Reply functionality
    $(document).on('click', '.reply-btn', function() {
        let commentId = $(this).data('comment-id');
        let userName = $(this).data('user-name');
        
        // Hide all other reply forms
        $('.reply-form').hide();
        
        // Show this reply form
        $(`#reply-form-${commentId}`).show();
        
        // Focus on textarea and add @mention
        let textarea = $(`#reply-form-${commentId} .reply-textarea`);
        textarea.focus();
        textarea.val(`@${userName} `);
    });

    $(document).on('click', '.cancel-reply', function() {
        let commentId = $(this).data('comment-id');
        $(`#reply-form-${commentId}`).hide();
        $(`#reply-form-${commentId} .reply-textarea`).val('');
    });

    $(document).on('click', '.submit-reply', function() {
    let commentId = $(this).data('comment-id');
    let replyText = $(`#reply-form-${commentId} .reply-textarea`).val();
    
    if (replyText.trim() === '') {
        alert('Please enter a reply');
        return;
    }
    
    // Submit reply to backend instead of just frontend
    submitReplyToBackend(commentId, replyText);
});

// Add this new function to actually save to database
function submitReplyToBackend(parentId, replyText) {
    $.ajax({
        url: '<?php echo e(route("leads.comments.store", $lead->id)); ?>',
        method: 'POST',
        data: {
            comment: replyText,
            parent_id: parentId,
            _token: $('meta[name="csrf-token"]').attr('content')
        },
        success: function(response) {
            console.log('Reply saved successfully');
            // Hide reply form and clear textarea
            $(`#reply-form-${parentId}`).hide();
            $(`#reply-form-${parentId} .reply-textarea`).val('');
            // Reload page to show new reply
            location.reload();
        },
        error: function(xhr, status, error) {
            console.error('Reply error:', xhr.responseText);
            console.log('Data sent:', {
                comment: replyText,
                parent_id: parentId
            });
            alert('Error submitting reply');
        }
    });
}

    // Mention functionality
    $('#comment').on('input', function(e) {
        let text = $(this).val();
        let cursorPos = this.selectionStart;
        let atIndex = text.lastIndexOf('@', cursorPos - 1);
        
        if (atIndex !== -1) {
            let textAfterAt = text.substring(atIndex + 1, cursorPos);
            if (!textAfterAt.includes(' ')) {
                showMentions(textAfterAt);
            } else {
                $('#mention-dropdown').hide();
            }
        } else {
            $('#mention-dropdown').hide();
        }
    });

    function showMentions(query) {
        let filtered = users.filter(u => u.name.toLowerCase().includes(query.toLowerCase()));
        let html = '';
        filtered.forEach(user => {
            html += `<li class="list-group-item mention-item" data-name="${user.name}">${user.name}</li>`;
        });
        $('#mention-list').html(html);
        if (filtered.length > 0) {
            $('#mention-dropdown').show();
        }
    }

    $(document).on('click', '.mention-item', function() {
        let name = $(this).data('name');
        let text = $('#comment').val();
        let newText = text.replace(/@\w*$/, `@${name} `);
        $('#comment').val(newText);
        $('#mention-dropdown').hide();
    });

    // File deletion functionality is now handled in the main script section above
});

// Add this function after the document ready block or inside it
function deleteComment(commentId) {
    // Removed confirm dialog as requested
    $.ajax({
        url: '/leads/comments/' + commentId,
        type: 'DELETE',
        data: {
            _token: $('meta[name="csrf-token"]').attr('content')
        },
        success: function(response) {
            if (response.success) {
                // Remove the comment or reply from the DOM
                // Try to remove reply-item first, if not found, remove comment-item
                var el = $(
                    '.reply-item:has([onclick*="deleteComment(' + commentId + ')"])'
                );
                if (el.length === 0) {
                    el = $(
                        '.comment-item:has([onclick*="deleteComment(' + commentId + ')"])'
                    );
                }
                el.fadeOut(300, function() { $(this).remove(); });
                // Show success message
                if (typeof show_toastr === 'function') {
                    show_toastr('success', response.message || 'Comment deleted successfully', 'success');
                } else {
                    alert(response.message || 'Comment deleted successfully');
                }
            } else {
                if (typeof show_toastr === 'function') {
                    show_toastr('error', response.message || 'Error deleting comment', 'error');
                } else {
                    alert(response.message || 'Error deleting comment');
                }
            }
        },
        error: function(xhr) {
            if (typeof show_toastr === 'function') {
                show_toastr('error', 'Error deleting comment', 'error');
            } else {
                alert('Error deleting comment');
            }
        }
    });
}

// Add or update this function after document ready
function updateReactionCounts(commentId, reactions) {
    let countsHtml = '';
    const icons = { 'like': '👍', 'love': '❤️' };
    for (let reaction in reactions) {
        if (reactions[reaction] > 0) {
            countsHtml += `<span class="badge bg-light text-dark me-1">${icons[reaction] || ''} ${reactions[reaction]}</span>`;
        }
    }
    $(`#reaction-counts-${commentId}`).html(countsHtml);
}
</script>
<?php $__env->stopPush(); ?>
<?php $__env->startPush('script-page'); ?>
<script>
$(document).on('click', '.lead-task-details-link', function(e) {
    e.preventDefault();
    var url = $(this).data('url');
    $('#leadTaskDetailsModal .modal-body').html('<div class="text-center py-5"><i class="ti ti-loader"></i></div>');
    $('#leadTaskDetailsModal').modal('show');
    $.get(url, function(data) {
        $('#leadTaskDetailsModal .modal-body').html(data);
    });
});
</script>
<?php $__env->stopPush(); ?>
<?php $__env->stopSection(); ?>
<?php echo $__env->make('layouts.admin', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH C:\xampp\htdocs\omx-new-saas\resources\views/leads/show.blade.php ENDPATH**/ ?>