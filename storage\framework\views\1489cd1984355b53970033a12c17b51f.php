<?php echo e(Form::open(array('route' => ['leads.labels.store',$lead->id]))); ?>

<div class="modal-body">
    <!-- Existing Labels Section -->
    <div class="row">
        <div class="col-12 form-group">
            <h6 class="mb-3"><?php echo e(__('Select Existing Labels')); ?></h6>
            <div class="row gutters-xs">
                <?php if(count($labels) > 0): ?>
                    <?php $__currentLoopData = $labels; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $label): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                        <div class="col-12 custom-control custom-checkbox mt-2 mb-2 d-flex align-items-center justify-content-between">
                            <div class="d-flex align-items-center">
                                <?php echo e(Form::checkbox('labels[]',$label->id,(array_key_exists($label->id,$selected))?true:false,['class' => 'form-check-input','id'=>'labels_'.$label->id])); ?>

                                <?php echo e(Form::label('labels_'.$label->id, ucfirst($label->name),['class'=>'custom-control-label ml-4 text-white p-2 px-3 rounded status_badge badge bg-'.$label->color])); ?>

                            </div>
                            <div class="d-flex gap-1">
                                <button type="button" class="btn btn-sm btn-outline-primary edit-label-btn"
                                    data-label-id="<?php echo e($label->id); ?>"
                                    data-label-name="<?php echo e($label->name); ?>"
                                    data-label-color="<?php echo e($label->color); ?>"
                                    title="<?php echo e(__('Edit Label')); ?>">
                                    <i class="ti ti-pencil"></i>
                                </button>
                                <button type="button" class="btn btn-sm btn-outline-danger delete-label-btn"
                                    data-label-id="<?php echo e($label->id); ?>"
                                    data-label-name="<?php echo e($label->name); ?>"
                                    title="<?php echo e(__('Delete Label')); ?>">
                                    <i class="ti ti-trash"></i>
                                </button>
                            </div>
                        </div>
                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                <?php else: ?>
                    <div class="col-12">
                        <div class="text-center py-4">
                            <div class="mb-3">
                                <i class="ti ti-tag text-muted" style="font-size: 3rem;"></i>
                            </div>
                            <h6 class="text-muted"><?php echo e(__('No Labels Available')); ?></h6>
                            <p class="text-muted small"><?php echo e(__('Create your first label using the form below to organize your leads better.')); ?></p>
                        </div>
                    </div>
                <?php endif; ?>
            </div>
        </div>
    </div>

    <!-- Edit Label Section (Hidden by default) -->
    <div id="edit-label-section" style="display: none;">
        <hr class="my-4">
        <div class="row">
            <div class="col-12">
                <h6 class="mb-3"><?php echo e(__('Edit Label')); ?></h6>

                <div class="form-group mb-3">
                    <?php echo e(Form::label('edit_label_name', __('Label Name'), ['class' => 'form-label'])); ?>

                    <?php echo e(Form::text('edit_label_name', '', ['class' => 'form-control', 'placeholder' => __('Enter Label Name'), 'id' => 'edit_label_name'])); ?>

                </div>

                <div class="form-group mb-3">
                    <?php echo e(Form::label('edit_label_color', __('Color'), ['class' => 'form-label'])); ?>

                    <div class="row gutters-xs">
                        <?php ($colors = ['primary', 'secondary', 'danger', 'warning', 'info', 'success']); ?>
                        <?php $__currentLoopData = $colors; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $color): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                            <div class="col-auto">
                                <label class="colorinput">
                                    <input name="edit_label_color" type="radio" value="<?php echo e($color); ?>" class="colorinput-input">
                                    <span class="colorinput-color bg-<?php echo e($color); ?>"></span>
                                </label>
                            </div>
                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                    </div>
                </div>

                <div class="d-flex gap-2 mb-3">
                    <button type="button" class="btn btn-success btn-sm" id="save-edit-label"><?php echo e(__('Save Changes')); ?></button>
                    <button type="button" class="btn btn-secondary btn-sm" id="cancel-edit-label"><?php echo e(__('Cancel')); ?></button>
                </div>

                <?php echo e(Form::hidden('edit_label_id', '', ['id' => 'edit_label_id'])); ?>

            </div>
        </div>
    </div>

    <!-- Create New Label Section -->
    <hr class="my-4">
    <div class="row" id="create-label-section">
        <div class="col-12">
            <h6 class="mb-3"><?php echo e(__('Create New Label')); ?></h6>

            <div class="form-group mb-3">
                <?php echo e(Form::label('new_label_name', __('Label Name'), ['class' => 'form-label'])); ?>

                <?php echo e(Form::text('new_label_name', '', ['class' => 'form-control', 'placeholder' => __('Enter Label Name'), 'id' => 'new_label_name'])); ?>

            </div>

            <div class="form-group mb-3">
                <?php echo e(Form::label('new_label_color', __('Color'), ['class' => 'form-label'])); ?>

                <div class="row gutters-xs">
                    <?php ($colors = ['primary', 'secondary', 'danger', 'warning', 'info', 'success']); ?>
                    <?php $__currentLoopData = $colors; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $color): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                        <div class="col-auto">
                            <label class="colorinput">
                                <input name="new_label_color" type="radio" value="<?php echo e($color); ?>" class="colorinput-input">
                                <span class="colorinput-color bg-<?php echo e($color); ?>"></span>
                            </label>
                        </div>
                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                </div>
            </div>
        </div>
    </div>
</div>

<div class="modal-footer">
    <input type="button" value="<?php echo e(__('Cancel')); ?>" class="btn btn-secondary" data-bs-dismiss="modal">
    <input type="submit" value="<?php echo e(__('Save')); ?>" class="btn btn-primary">
</div>

<?php echo e(Form::close()); ?>


<script>
$(document).ready(function() {
    // Handle form submission
    $('form').on('submit', function(e) {
        var newLabelName = $('#new_label_name').val().trim();
        var newLabelColor = $('input[name="new_label_color"]:checked').val();

        // If user is creating a new label, validate the fields
        if (newLabelName || newLabelColor) {
            if (!newLabelName) {
                e.preventDefault();
                show_toastr('error', '<?php echo e(__("Please enter a label name")); ?>');
                return false;
            }
            if (!newLabelColor) {
                e.preventDefault();
                show_toastr('error', '<?php echo e(__("Please select a color for the label")); ?>');
                return false;
            }
        }
    });

    // Handle edit label button click
    $('.edit-label-btn').on('click', function() {
        var labelId = $(this).data('label-id');
        var labelName = $(this).data('label-name');
        var labelColor = $(this).data('label-color');

        // Show edit section and hide create section
        $('#edit-label-section').show();
        $('#create-label-section').hide();

        // Populate edit form
        $('#edit_label_id').val(labelId);
        $('#edit_label_name').val(labelName);
        $('input[name="edit_label_color"][value="' + labelColor + '"]').prop('checked', true);
    });

    // Handle cancel edit
    $('#cancel-edit-label').on('click', function() {
        $('#edit-label-section').hide();
        $('#create-label-section').show();
        clearEditForm();
    });

    // Handle save edit label
    $('#save-edit-label').on('click', function() {
        var labelId = $('#edit_label_id').val();
        var labelName = $('#edit_label_name').val().trim();
        var labelColor = $('input[name="edit_label_color"]:checked').val();

        if (!labelName) {
            show_toastr('error', '<?php echo e(__("Please enter a label name")); ?>');
            return;
        }
        if (!labelColor) {
            show_toastr('error', '<?php echo e(__("Please select a color for the label")); ?>');
            return;
        }

        // Send AJAX request to update label
        $.ajax({
            url: '<?php echo e(route("labels.update", ":id")); ?>'.replace(':id', labelId),
            type: 'PUT',
            data: {
                name: labelName,
                color: labelColor,
                pipeline_id: '<?php echo e($lead->pipeline_id); ?>',
                _token: $('meta[name="csrf-token"]').attr('content')
            },
            success: function(response) {
                show_toastr('success', '<?php echo e(__("Label updated successfully!")); ?>');
                setTimeout(function() {
                    location.reload();
                }, 1000);
            },
            error: function(xhr) {
                var errorMessage = 'An error occurred';
                if (xhr.responseJSON && xhr.responseJSON.message) {
                    errorMessage = xhr.responseJSON.message;
                }
                show_toastr('error', errorMessage);
            }
        });
    });

    // Handle delete label button click
    $('.delete-label-btn').on('click', function() {
        var labelId = $(this).data('label-id');
        var labelName = $(this).data('label-name');

        if (confirm('<?php echo e(__("Are you sure you want to delete the label")); ?> "' + labelName + '"?')) {
            $.ajax({
                url: '<?php echo e(route("labels.destroy", ":id")); ?>'.replace(':id', labelId),
                type: 'DELETE',
                data: {
                    _token: $('meta[name="csrf-token"]').attr('content')
                },
                success: function(response) {
                    show_toastr('success', '<?php echo e(__("Label deleted successfully!")); ?>');
                    setTimeout(function() {
                        location.reload();
                    }, 1000);
                },
                error: function(xhr) {
                    var errorMessage = 'An error occurred';
                    if (xhr.responseJSON && xhr.responseJSON.message) {
                        errorMessage = xhr.responseJSON.message;
                    }
                    show_toastr('error', errorMessage);
                }
            });
        }
    });

    // Clear edit form
    function clearEditForm() {
        $('#edit_label_id').val('');
        $('#edit_label_name').val('');
        $('input[name="edit_label_color"]').prop('checked', false);
    }

    // Clear new label fields when modal is closed
    $('.modal').on('hidden.bs.modal', function() {
        $('#new_label_name').val('');
        $('input[name="new_label_color"]').prop('checked', false);
        $('#edit-label-section').hide();
        $('#create-label-section').show();
        clearEditForm();
    });
});
</script>

<?php /**PATH C:\xampp\htdocs\omx-new-saas\resources\views/leads/labels.blade.php ENDPATH**/ ?>