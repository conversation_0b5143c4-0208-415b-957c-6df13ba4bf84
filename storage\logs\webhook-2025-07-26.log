[2025-07-26 06:14:11] local.INFO: Starting webhook dispatch for action: booking.event_created {"timestamp":"2025-07-26T06:14:11.325762Z","source":"crm_webhook_system","action":"booking.event_created","user_id":84,"entity_type":null,"entity_id":null,"status":"dispatching"} 
[2025-07-26 06:14:15] local.ERROR: Webhook failed for OMX FLOW: cURL error 7: Failed to connect to 127.0.0.1 port 2000 after 2033 ms: Couldn't connect to server (see https://curl.haxx.se/libcurl/c/libcurl-errors.html) for http://127.0.0.1:2000/external-crm/webhook {"timestamp":"2025-07-26T06:14:15.134304Z","source":"crm_webhook_system","action":"booking.event_created","module_name":"OMX FLOW","webhook_url":"http://127.0.0.1:2000/external-crm/webhook","status":"failed","status_code":null,"response_time_ms":3759.0,"user_id":84,"entity_id":4,"entity_type":null,"request_payload":{"action":"booking.event_created","timestamp":"2025-07-26T06:14:11.375214Z","data":{"id":4,"title":"AI Bot Live","start_date":"2025-07-26 06:13:57","end_date":"2026-07-26 06:13:57","duration":60,"booking_per_slot":1,"minimum_notice":60,"description":"This is the best live AI agent.","location":"in_person","meet_link":"8617555736","physical_address":"https://maps.app.goo.gl/XvDSMjZrKaR9ifpy5","custom_fields":null,"date_override":null,"created_by":84,"slots_created":2928,"triggered_by":{"user_id":84,"email":"<EMAIL>","name":"parichay  Bot flow","type":"company"}},"user_id":84,"source":{"system":"krishna","version":"1.0","url":"http://localhost:8000"}},"error_message":"cURL error 7: Failed to connect to 127.0.0.1 port 2000 after 2033 ms: Couldn't connect to server (see https://curl.haxx.se/libcurl/c/libcurl-errors.html) for http://127.0.0.1:2000/external-crm/webhook","response_body":null} 
[2025-07-26 06:14:15] local.WARNING: Webhook dispatch completed for action: booking.event_created. Success: 0, Failed: 1 {"timestamp":"2025-07-26T06:14:15.135679Z","source":"crm_webhook_system","action":"booking.event_created","user_id":84,"status":"completed","total_modules":1,"successful_modules":0,"failed_modules":1,"modules":["OMX FLOW"],"results":{"OMX FLOW":{"success":false,"error":"cURL error 7: Failed to connect to 127.0.0.1 port 2000 after 2033 ms: Couldn't connect to server (see https://curl.haxx.se/libcurl/c/libcurl-errors.html) for http://127.0.0.1:2000/external-crm/webhook","integration":"OMX FLOW"}}} 
[2025-07-26 06:44:56] local.INFO: Starting webhook dispatch for action: booking.booking_form_submitted {"timestamp":"2025-07-26T06:44:56.741078Z","source":"crm_webhook_system","action":"booking.booking_form_submitted","user_id":84,"entity_type":"Booking","entity_id":5,"status":"dispatching"} 
[2025-07-26 06:45:02] local.ERROR: Webhook failed for OMX FLOW: cURL error 7: Failed to connect to 127.0.0.1 port 2000 after 2057 ms: Couldn't connect to server (see https://curl.haxx.se/libcurl/c/libcurl-errors.html) for http://127.0.0.1:2000/external-crm/webhook {"timestamp":"2025-07-26T06:45:02.013624Z","source":"crm_webhook_system","action":"booking.booking_form_submitted","module_name":"OMX FLOW","webhook_url":"http://127.0.0.1:2000/external-crm/webhook","status":"failed","status_code":null,"response_time_ms":5197.0,"user_id":84,"entity_id":5,"entity_type":null,"request_payload":{"action":"booking.booking_form_submitted","timestamp":"2025-07-26T06:44:56.817070Z","data":{"event_id":4,"name":"Parichay Singha Parichay Singha","email":"<EMAIL>","phone":"08617555736","date":"2025-07-30","time":"14:30","selected_location":{"type":"in_person","value":"https://maps.app.goo.gl/XvDSMjZrKaR9ifpy5","display":"In-person meeting"},"custom_fields":[],"custom_fields_value":[],"updated_at":"2025-07-26T06:44:56.000000Z","created_at":"2025-07-26T06:44:56.000000Z","id":5,"form_data":{"name":"Parichay Singha Parichay Singha","email":"<EMAIL>","phone":"08617555736","date":"2025-07-30","time":"14:30","selected_location":{"type":"in_person","value":"https://maps.app.goo.gl/XvDSMjZrKaR9ifpy5","display":"In-person meeting"},"custom_fields":[],"custom_fields_value":[],"booking_type":"public"},"triggered_by":{"user_id":84,"email":"<EMAIL>","name":"parichay  Bot flow","type":"company"}},"user_id":84,"source":{"system":"krishna","version":"1.0","url":"http://localhost:8000"}},"error_message":"cURL error 7: Failed to connect to 127.0.0.1 port 2000 after 2057 ms: Couldn't connect to server (see https://curl.haxx.se/libcurl/c/libcurl-errors.html) for http://127.0.0.1:2000/external-crm/webhook","response_body":null} 
[2025-07-26 06:45:02] local.WARNING: Webhook dispatch completed for action: booking.booking_form_submitted. Success: 0, Failed: 1 {"timestamp":"2025-07-26T06:45:02.014903Z","source":"crm_webhook_system","action":"booking.booking_form_submitted","user_id":84,"status":"completed","total_modules":1,"successful_modules":0,"failed_modules":1,"modules":["OMX FLOW"],"results":{"OMX FLOW":{"success":false,"error":"cURL error 7: Failed to connect to 127.0.0.1 port 2000 after 2057 ms: Couldn't connect to server (see https://curl.haxx.se/libcurl/c/libcurl-errors.html) for http://127.0.0.1:2000/external-crm/webhook","integration":"OMX FLOW"}}} 
