
<?php $__env->startSection('page-title'); ?>
    <?php echo e(__('Manage Leads')); ?> <?php if($pipeline): ?>
        - <?php echo e($pipeline->name); ?>

    <?php endif; ?>
<?php $__env->stopSection(); ?>

<?php $__env->startPush('css-page'); ?>
    <link rel="stylesheet" href="<?php echo e(asset('css/summernote/summernote-bs4.css')); ?>">
    <link rel="stylesheet" href="<?php echo e(asset('assets/css/plugins/dragula.min.css')); ?>" id="main-style-link">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.4/css/all.min.css">
    <style>
        :root {
            --kanban-bg: rgba(255,255,255,0.7);
            --kanban-blur: 16px;
            --kanban-shadow: 0 8px 32px 0 rgba(31, 38, 135, 0.10);
            --kanban-border: 1px solid rgba(255,255,255,0.18);
            --kanban-radius: 18px;
            --kanban-gradient: linear-gradient(135deg, rgba(245,247,250,0.8) 0%, rgba(230,240,255,0.7) 100%);
            --kanban-label-radius: 12px;
            --kanban-label-font: 13px;
            --kanban-label-padding: 3px 12px;
        }
        .kanban-wrapper {
            display: flex;
            gap: 24px;
            overflow-x: auto;
            padding-bottom: 16px;
            scrollbar-width: thin;
        }
        .kanban-wrapper::-webkit-scrollbar {
            height: 8px;
        }
        .kanban-wrapper::-webkit-scrollbar-thumb {
            background: #e0e7ef;
            border-radius: 8px;
        }
        .kanban-col {
            min-width: 320px;
            max-width: 350px;
            flex: 1 0 320px;
            background: var(--kanban-gradient);
            border-radius: var(--kanban-radius);
            box-shadow: var(--kanban-shadow);
            border: var(--kanban-border);
            backdrop-filter: blur(var(--kanban-blur));
            padding: 0 0 12px 0;
            display: flex;
            flex-direction: column;
            height: 100%;
        }
        .kanban-header {
            padding: 20px 20px 10px 20px;
            border-bottom: 1px solid #f0f0f0;
            background: transparent;
            border-radius: var(--kanban-radius) var(--kanban-radius) 0 0;
            display: flex;
            align-items: center;
            justify-content: space-between;
        }
        .kanban-header h4 {
            font-size: 1.1rem;
            font-weight: 700;
            margin: 0;
        }
        .kanban-header .count {
            background: #e3e9f7;
            color: #3a3a3a;
            border-radius: 50%;
            width: 28px;
            height: 28px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: 600;
            font-size: 1rem;
        }
        .sales-item-wrp {
            padding: 16px 12px 0 12px;
            min-height: 80px;
            max-height: 480px; /* Show ~3 cards, then scroll */
            overflow-y: auto;
            scrollbar-width: thin;
            scrollbar-color: #e0e7ef #f8fafc;
            transition: max-height 0.2s;
        }
        .sales-item-wrp::-webkit-scrollbar {
            width: 8px;
        }
        .sales-item-wrp::-webkit-scrollbar-thumb {
            background: #e0e7ef;
            border-radius: 8px;
        }
        .sales-item-wrp::-webkit-scrollbar-track {
            background: #f8fafc;
            border-radius: 8px;
        }
        .kanban-card {
            background: var(--kanban-bg);
            border-radius: var(--kanban-radius);
            box-shadow: 0 2px 12px 0 rgba(31, 38, 135, 0.08);
            border: var(--kanban-border);
            margin-bottom: 18px;
            padding: 18px 16px 12px 16px;
            position: relative;
            transition: box-shadow 0.2s, transform 0.2s;
            cursor: grab;
            backdrop-filter: blur(var(--kanban-blur));
        }
        .kanban-card.dragging {
            box-shadow: 0 8px 32px 0 rgba(31, 38, 135, 0.18);
            transform: scale(1.03);
            z-index: 10;
        }
        .kanban-card .card-top {
            display: flex;
            align-items: flex-start;
            justify-content: space-between;
        }
        .kanban-card .lead-title {
            font-size: 1.05rem;
            font-weight: 600;
            color: #222;
            margin-bottom: 2px;
            display: flex;
            align-items: center;
            gap: 6px;
        }
        .kanban-card .lead-title i {
            color: #6c63ff;
            font-size: 1rem;
        }
        .kanban-card .badge-wrp {
            margin-top: 6px;
            display: flex;
            gap: 6px;
            flex-wrap: wrap;
        }
        .kanban-label {
            border-radius: var(--kanban-label-radius);
            font-size: var(--kanban-label-font);
            padding: var(--kanban-label-padding);
            font-weight: 500;
            color: #fff;
            background: #6c63ff;
            opacity: 0.95;
        }
        .kanban-label.bg-light-success { background: #28a745; }
        .kanban-label.bg-light-danger { background: #dc3545; }
        .kanban-label.bg-light-warning { background: #ffc107; color: #222; }
        .kanban-label.bg-light-info { background: #17a2b8; }
        .kanban-label.bg-light-primary { background: #6c63ff; }
        .kanban-label.bg-light-secondary { background: #6c757d; }
        .kanban-label.bg-light-brown { background: #a17a69; }
        .kanban-label.bg-light-blue { background: #007bff; }
        .kanban-label.bg-light-purple { background: #6f42c1; }
        .kanban-card .contact-info {
            margin-top: 10px;
            display: flex;
            flex-direction: column;
            gap: 6px;
        }
        .kanban-card .contact-item {
            display: flex;
            align-items: center;
            gap: 7px;
            font-size: 14px;
            color: #555;
        }
        .kanban-card .contact-item i {
            color: #888;
        }
        .kanban-card .card-bottom {
            margin-top: 14px;
            display: flex;
            align-items: center;
            justify-content: space-between;
        }
        .kanban-card .communication-buttons {
            display: flex;
            gap: 8px;
        }
        .kanban-card .communication-btn {
            width: 32px;
            height: 32px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            color: #fff;
            cursor: pointer;
            transition: all 0.3s ease;
            border: none;
            box-shadow: 0 2px 5px rgba(0,0,0,0.12);
            background: #6c63ff;
        }
        .kanban-card .communication-btn.call { background: #28a745; }
        .kanban-card .communication-btn.sms { background: #17a2b8; }
        .kanban-card .communication-btn.email { background: #6f42c1; }
        .kanban-card .communication-btn i {
            font-size: 14px;
        }
        .kanban-card .user-group {
            display: flex;
            gap: 4px;
        }
        .kanban-card .user-group i {
            font-size: 22px;
            color: #6c63ff;
        }
        .kanban-card .drag-handle {
            cursor: grab;
            color: #bdbdbd;
            font-size: 18px;
            margin-right: 8px;
            transition: color 0.2s;
        }
        .kanban-card .drag-handle:hover {
            color: #6c63ff;
        }
        /* Responsive */
        @media (max-width: 900px) {
            .kanban-wrapper {
                gap: 12px;
            }
            .kanban-col {
                min-width: 260px;
                max-width: 100vw;
            }
            .sales-item-wrp {
                max-height: 340px;
            }
        }
        @media (max-width: 600px) {
            .kanban-wrapper {
                gap: 8px;
            }
            .kanban-col {
                min-width: 90vw;
                max-width: 98vw;
                padding: 0;
            }
            .kanban-header {
                padding: 14px 10px 8px 10px;
            }
            .sales-item-wrp {
                max-height: 220px;
                padding: 8px 4px 0 4px;
            }
            .kanban-card {
                padding: 12px 8px 8px 8px;
            }
        }
    </style>
    <style>
        .modern-comm-modal {
            background: rgba(255,255,255,0.85);
            border-radius: 20px;
            box-shadow: 0 8px 32px 0 rgba(31, 38, 135, 0.18);
            border: 1px solid rgba(255,255,255,0.18);
            backdrop-filter: blur(12px);
        }
        .comm-lead-avatar {
            width: 48px;
            height: 48px;
            border-radius: 50%;
            background: linear-gradient(135deg, #6c63ff 0%, #17a2b8 100%);
            color: #fff;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 1.5rem;
            font-weight: 700;
            box-shadow: 0 2px 8px rgba(31,38,135,0.10);
        }
        .comm-lead-name {
            font-weight: 600;
            font-size: 1.1rem;
        }
        .comm-lead-contact {
            font-size: 0.95rem;
        }
        .communication-options-list {
            display: flex;
            flex-direction: column;
            gap: 14px;
            margin-top: 10px;
        }
        .communication-option-item {
            display: flex;
            align-items: center;
            padding: 14px 18px;
            border-radius: 12px;
            background: rgba(245,247,250,0.95);
            color: #333;
            text-decoration: none;
            font-size: 1.08rem;
            font-weight: 500;
            box-shadow: 0 2px 8px rgba(31,38,135,0.06);
            transition: all 0.2s;
            border: 1px solid #f0f0f0;
        }
        .communication-option-item:hover {
            background: linear-gradient(135deg, #e3e9f7 0%, #f8fafc 100%);
            transform: translateX(5px) scale(1.03);
            box-shadow: 0 4px 16px rgba(31,38,135,0.10);
        }
        .communication-option-item i {
            font-size: 1.5rem;
            margin-right: 18px;
            width: 32px;
            text-align: center;
        }
        #whatsapp-option i { color: #25D366; }
        #default-email-option i { color: #007BFF; }
        #cloud-email-option i { color: #6f42c1; }
        #sms-option i { color: #17a2b8; }
        @media (max-width: 600px) {
            .modern-comm-modal {
                border-radius: 10px;
            }
            .communication-option-item {
                padding: 12px 10px;
                font-size: 1rem;
            }
            .comm-lead-avatar {
                width: 38px;
                height: 38px;
                font-size: 1.1rem;
            }
        }
    </style>
<?php $__env->stopPush(); ?>
<?php $__env->startPush('script-page'); ?>
    <script src="<?php echo e(asset('css/summernote/summernote-bs4.js')); ?>"></script>
    <script src="<?php echo e(asset('assets/js/plugins/dragula.min.js')); ?>"></script>
    <script>
        ! function(a) {
            "use strict";
            var t = function() {
                this.$body = a("body");
                this.dragulaInstance = null;
                this.dragTimeout = null;
            };

            t.prototype.init = function() {
                var self = this;

                a('[data-plugin="dragula"]').each(function() {
                    var $wrapper = a(this);
                    var containers = $wrapper.data("containers");
                    var containerElements = [];

                    if (containers) {
                        for (var i = 0; i < containers.length; i++) {
                            var element = a("#" + containers[i])[0];
                            if (element) containerElements.push(element);
                        }
                    } else {
                        containerElements = [$wrapper[0]];
                    }

                    var handleClass = $wrapper.data("handleclass");

                    // Destroy existing instance if it exists
                    if (self.dragulaInstance) {
                        self.dragulaInstance.destroy();
                    }

                    // Create optimized dragula instance
                    self.dragulaInstance = dragula(containerElements, {
                        moves: function(el, container, handle) {
                            if (handleClass) {
                                return handle.classList.contains(handleClass);
                            }
                            // Allow dragging on card but not on interactive elements
                            return !a(handle).closest('a, button, .btn, .dropdown-menu, input, textarea, select').length;
                        },
                        accepts: function(el, target, source, sibling) {
                            return a(target).hasClass('sales-item-wrp');
                        },
                        removeOnSpill: false,
                        revertOnSpill: true,
                        direction: 'vertical',
                        mirrorContainer: document.body
                    });

                    // Add optimized event handlers
                    self.dragulaInstance.on('drag', function(el, source) {
                        a(el).addClass('gu-transit-optimized');
                        a('body').addClass('dragging-leads');

                        // Disable text selection during drag
                        a('body').css('user-select', 'none');
                    });

                    self.dragulaInstance.on('dragend', function(el) {
                        a(el).removeClass('gu-transit-optimized');
                        a('body').removeClass('dragging-leads');

                        // Re-enable text selection
                        a('body').css('user-select', '');
                    });

                    self.dragulaInstance.on('over', function(el, container, source) {
                        a(container).addClass('drag-over-highlight');
                    });

                    self.dragulaInstance.on('out', function(el, container, source) {
                        a(container).removeClass('drag-over-highlight');
                    });

                    self.dragulaInstance.on('drop', function(el, target, source, sibling) {
                        // Clear any existing timeout
                        if (self.dragTimeout) {
                            clearTimeout(self.dragTimeout);
                        }

                        // Remove drag classes immediately for better UX
                        a(el).removeClass('gu-transit-optimized');
                        a(target).removeClass('drag-over-highlight');
                        a('body').removeClass('dragging-leads');
                        a('body').css('user-select', '');

                        // Get data efficiently
                        var leadId = a(el).attr('data-id');
                        var sourceId = a(source).attr('id');
                        var targetId = a(target).attr('id');
                        var stageId = a(target).attr('data-id');
                        var oldStatus = a(source).data('status');
                        var newStatus = a(target).data('status');
                        var pipelineId = '<?php echo e($pipeline->id); ?>';

                        // Update counts immediately for better UX
                        var sourceCount = a("#" + sourceId + " > div").length;
                        var targetCount = a("#" + targetId + " > div").length;

                        a("#" + sourceId).parent().find('.count').text(sourceCount);
                        a("#" + targetId).parent().find('.count').text(targetCount);

                        // Collect order efficiently
                        var order = [];
                        a("#" + targetId + " > div").each(function(index) {
                            var dataId = a(this).attr('data-id');
                            if (dataId) order.push(dataId);
                        });

                        // Add loading state
                        a(el).addClass('updating-lead');

                        // Debounced AJAX call
                        self.dragTimeout = setTimeout(function() {
                            a.ajax({
                                url: '<?php echo e(route('lead_stages.moveLeadToStage')); ?>',
                                type: 'POST',
                                data: {
                                    lead_id: leadId,
                                    stage_id: stageId,
                                    order: order,
                                    new_status: newStatus,
                                    old_status: oldStatus,
                                    pipeline_id: pipelineId,
                                    "_token": a('meta[name="csrf-token"]').attr('content')
                                },
                                success: function(data) {
                                    a(el).removeClass('updating-lead');
                                    if (data.status === 'success') {
                                        show_toastr('success', data.message, 'success');
                                    } else {
                                        show_toastr(data.status, data.message, data.status);
                                    }
                                },
                                error: function(xhr) {
                                    a(el).removeClass('updating-lead');
                                    var data = xhr.responseJSON || {};
                                    show_toastr('error', data.message || 'An error occurred', 'error');

                                    // Revert the move on error
                                    if (source !== target) {
                                        a(source).append(el);
                                        // Restore counts
                                        a("#" + sourceId).parent().find('.count').text(a("#" + sourceId + " > div").length);
                                        a("#" + targetId).parent().find('.count').text(a("#" + targetId + " > div").length);
                                    }
                                }
                            });
                        }, 100); // Small delay to prevent rapid fire requests
                    });
                });
            };

            a.Dragula = new t;
            a.Dragula.Constructor = t;
        }(window.jQuery),
        function(a) {
            "use strict";

            // Initialize dragula with proper timing
            a(document).ready(function() {
                // Small delay to ensure DOM is fully rendered
                setTimeout(function() {
                    a.Dragula.init();
                }, 100);
            });

            // Reinitialize on dynamic content updates
            a(document).on('contentUpdated', function() {
                setTimeout(function() {
                    a.Dragula.init();
                }, 100);
            });

        }(window.jQuery);

        function openSourcesModal(sources) {
            const sourcesList = document.getElementById('sources-list');
            sourcesList.innerHTML = ''; // Clear previous sources

            if (sources && sources.length > 0) {
                sources.forEach(source => {
                    const listItem = document.createElement('li');
                    listItem.className = 'list-group-item';
                    listItem.textContent = source.name;
                    sourcesList.appendChild(listItem);
                });
            } else {
                const listItem = document.createElement('li');
                listItem.className = 'list-group-item';
                listItem.textContent = '<?php echo e(__('No sources found for this lead.')); ?>';
                sourcesList.appendChild(listItem);
            }

            const modal = new bootstrap.Modal(document.getElementById('sources-modal'));
            modal.show();
        }
    </script>
    <script>
        $(document).on("change", "#default_pipeline_id", function() {
            $('#change-pipeline').submit();
        });

        // Improved Kanban filter logic inspired by list.blade.php
        window.handleKanbanFilterSubmit = function(form) {
            var nameFilter = $(form).find('input[name="name"]').val().toLowerCase();
            var emailFilter = $(form).find('input[name="email"]').val().toLowerCase();
            var stageFilter = $(form).find('select[name="stage_id"]').val();
            var hasFilters = nameFilter || emailFilter || stageFilter;

            // Show/hide filter active badge
            if (hasFilters) {
                $('#filter-active-badge-kanban').removeClass('d-none');
            } else {
                $('#filter-active-badge-kanban').addClass('d-none');
            }

            var totalVisibleLeads = 0;
            // Filter each Kanban column
            $('.crm-sales-card').each(function() {
                var stageCard = $(this);
                var stageId = stageCard.find('.sales-item-wrp').data('id');
                var visibleLeadsCount = 0;

                // If stage filter is applied, hide/show entire stage
                if (stageFilter && stageFilter != stageId) {
                    stageCard.hide();
                    return; // Skip to next stage
                } else {
                    stageCard.show();
                }

                // Filter individual leads within this stage
                stageCard.find('.sales-item').each(function() {
                    var leadItem = $(this);
                    var leadName = leadItem.find('.lead-title a').text().toLowerCase();
                    var contactSpans = leadItem.find('.contact-info .contact-item span');
                    var leadEmail = contactSpans.length > 1 ? $(contactSpans[1]).text().toLowerCase() : '';

                    var showLead = true;
                    if (nameFilter && leadName.indexOf(nameFilter) === -1) {
                        showLead = false;
                    }
                    if (emailFilter && leadEmail.indexOf(emailFilter) === -1) {
                        showLead = false;
                    }
                    if (showLead) {
                        leadItem.show();
                        visibleLeadsCount++;
                        totalVisibleLeads++;
                    } else {
                        leadItem.hide();
                    }
                });
                // Update the count in stage header
                stageCard.find('.f-w-600').text(visibleLeadsCount);
                // Hide stage if no leads are visible
                if (visibleLeadsCount === 0 && hasFilters) {
                    stageCard.hide();
                } else {
                    stageCard.show();
                }
            });

            // Show/hide no results message
            if (totalVisibleLeads === 0 && hasFilters) {
                if ($('#no-kanban-results').length === 0) {
                    $('.kanban-wrapper').after('<div id="no-kanban-results" class="text-center py-4">'+
                        '<div class="alert alert-warning mb-0">'+
                        'No leads found matching the filter criteria.'+
                        '</div></div>');
                }
            } else {
                $('#no-kanban-results').remove();
            }

            // Close the off-canvas
            var offcanvasElement = document.getElementById('filterOffcanvasKanban');
            var offcanvas = bootstrap.Offcanvas.getInstance(offcanvasElement);
            if (offcanvas) {
                offcanvas.hide();
            }
            show_toastr('Success', 'Filters applied successfully', 'success');
            return false;
        };

        // Improved clearKanbanFilters function
        window.clearKanbanFilters = function() {
            $('#lead-filter-form-kanban')[0].reset();
            $('#lead-filter-form-kanban select').val('').trigger('change');
            $('#filter-active-badge-kanban').addClass('d-none');
            $('.crm-sales-card').show();
            $('.sales-item').show();
            $('.crm-sales-card').each(function() {
                var stageCard = $(this);
                var totalLeads = stageCard.find('.sales-item').length;
                stageCard.find('.f-w-600').text(totalLeads);
            });
            $('#no-kanban-results').remove();
            show_toastr('Success', 'Filters cleared successfully', 'success');
        };

        // Initialize Kanban off-canvas filter
        $(document).ready(function() {
            // Initialize select2 for the stage dropdown in Kanban off-canvas
            $('#filterOffcanvasKanban .select').select2({
                dropdownParent: $('#filterOffcanvasKanban'),
                placeholder: '<?php echo e(__("Select Stage")); ?>',
                allowClear: true
            });

            // Handle off-canvas shown event
            $('#filterOffcanvasKanban').on('shown.bs.offcanvas', function () {
                // Focus on first input when off-canvas is shown
                $(this).find('input[name="name"]').focus();
            });
        });

        // Handle Create Label Modal
        $('#createLabelModal').on('show.bs.modal', function (event) {
            var button = $(event.relatedTarget);
            var leadId = button.data('lead-id');
            var pipelineId = button.data('pipeline-id');

            var modal = $(this);
            modal.find('#modal_lead_id').val(leadId);
            modal.find('#modal_pipeline_id').val(pipelineId);
        });

        // Handle Create Label Form Submission
        $('#createLabelForm').on('submit', function(e) {
            e.preventDefault();

            var formData = $(this).serialize();
            var leadId = $('#modal_lead_id').val();

            $.ajax({
                url: $(this).attr('action'),
                type: 'POST',
                data: formData,
                headers: {
                    'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
                },
                success: function(response) {
                    $('#createLabelModal').modal('hide');
                    show_toastr('success', response.message || 'Label created successfully!');

                    // Reload the page to show the new label
                    setTimeout(function() {
                        location.reload();
                    }, 1000);
                },
                error: function(xhr) {
                    var errorMessage = 'An error occurred';
                    if (xhr.responseJSON && xhr.responseJSON.message) {
                        errorMessage = xhr.responseJSON.message;
                    } else if (xhr.responseJSON && xhr.responseJSON.errors) {
                        var errors = xhr.responseJSON.errors;
                        errorMessage = Object.values(errors).flat().join(', ');
                    }
                    show_toastr('error', errorMessage);
                }
            });
        });
    </script>
<?php $__env->stopPush(); ?>
<?php $__env->startSection('breadcrumb'); ?>
    <li class="breadcrumb-item"><a href="<?php echo e(route('dashboard')); ?>"><?php echo e(__('Dashboard')); ?></a></li>
    <li class="breadcrumb-item"><?php echo e(__('Lead')); ?></li>
<?php $__env->stopSection(); ?>
<?php $__env->startSection('action-btn'); ?>
    <div class="float-end ">
        <?php echo e(Form::open(['route' => 'deals.change.pipeline', 'id' => 'change-pipeline', 'class' => 'btn btn-sm'])); ?>

        <?php echo e(Form::select('default_pipeline_id', $pipelines, $pipeline->id, ['class' => 'form-control select me-2', 'id' => 'default_pipeline_id'])); ?>

        <?php echo e(Form::close()); ?>


        <a href="<?php echo e(route('leads.list')); ?>"
            data-size="lg"
            data-ajax-popup="true"
            data-bs-toggle="tooltip"
            data-bs-placement="bottom"
            title="<?php echo e(__('List View')); ?>"
            style="display:inline-flex;align-items:center;justify-content:center;width:40px;height:40px;
                    border-radius:50%;background:linear-gradient(to right, #065f46, #0f766e);color:white;
                    box-shadow:0 4px 6px rgba(0,0,0,0.1);transition:transform 0.2s ease-in-out;margin-right:0.25rem;"
            onmouseover="this.style.transform='scale(1.1)'"
            onmouseout="this.style.transform='scale(1)'">
            <i class="ti ti-list" style="font-size:16px;"></i>
        </a>

        <!-- Import Lead -->
        <a href="#"
            data-size="md"
            data-bs-toggle="tooltip"
            data-ajax-popup="true"
            data-bs-placement="bottom"
            title="<?php echo e(__('Import')); ?>"
            data-url="<?php echo e(route('leads.import')); ?>"
            data-ajax-popup="true"
            data-title="<?php echo e(__('Import Lead CSV file')); ?>"
            style="display:inline-flex;align-items:center;justify-content:center;width:40px;height:40px;
                    border-radius:50%;background:linear-gradient(to right, #065f46, #0f766e);color:white;
                    box-shadow:0 4px 6px rgba(0,0,0,0.1);transition:transform 0.2s ease-in-out;margin-right:0.5rem;"
            onmouseover="this.style.transform='scale(1.1)'"
            onmouseout="this.style.transform='scale(1)'">
            <i class="fas fa-file-import" style="font-size:16px;"></i>
        </a>
        <!-- Filter Leads -->
        <a href="#"
            data-bs-toggle="offcanvas"
            data-bs-target="#filterOffcanvasKanban"
            aria-controls="filterOffcanvasKanban"
            data-bs-placement="bottom"
            title="<?php echo e(__('Filter Leads')); ?>"
            id="filter-btn-kanban"
            style="display:inline-flex;align-items:center;justify-content:center;width:40px;height:40px; border-radius:50%;background:linear-gradient(to right, #065f46, #0f766e);color:white; box-shadow:0 4px 6px rgba(0,0,0,0.1);transition:transform 0.2s ease-in-out;margin-right:0.5rem;"
            onmouseover="this.style.transform='scale(1.1)'"
            onmouseout="this.style.transform='scale(1)'">
            <i class="ti ti-filter" style="font-size:16px;"></i>
            <span class="position-absolute top-0 start-100 translate-middle badge rounded-pill bg-danger d-none" id="filter-active-badge-kanban"></span>
        </a>
        <!-- Export Leads -->
        <a href="<?php echo e(route('leads.export')); ?>"
            data-bs-toggle="tooltip"
            data-bs-placement="bottom"
            title="<?php echo e(__('Export')); ?>"
            style="display:inline-flex;align-items:center;justify-content:center;width:40px;height:40px;
                    border-radius:50%;background:linear-gradient(to right, #065f46, #0f766e);color:white;
                    box-shadow:0 4px 6px rgba(0,0,0,0.1);transition:transform 0.2s ease-in-out;margin-right:0.5rem;"
            onmouseover="this.style.transform='scale(1.1)'"
            onmouseout="this.style.transform='scale(1)'">
            <i class="fas fa-file-export" style="font-size:16px;"></i>
        </a>
        <!-- Create New Lead -->
        <a href="#"
            data-size="lg"
            data-url="<?php echo e(route('leads.create')); ?>"
            data-ajax-popup="true"
            data-bs-toggle="tooltip"
            data-bs-placement="bottom"
            title="<?php echo e(__('Create New Lead')); ?>"
            data-title="<?php echo e(__('Create Lead')); ?>"
            style="display:inline-flex;align-items:center;justify-content:center;width:40px;height:40px;
                    border-radius:50%;background:linear-gradient(to right, #065f46, #0f766e);color:white;
                    box-shadow:0 4px 6px rgba(0,0,0,0.1);transition:transform 0.2s ease-in-out;margin-right:0.5rem;""
            onmouseover="this.style.transform='scale(1.1)'"
            onmouseout="this.style.transform='scale(1)'">
            <i class="ti ti-plus" style="font-size:16px;"></i>
        </a>
    </div>
<?php $__env->stopSection(); ?>

<?php $__env->startSection('content'); ?>
    <div class="row">
        <div class="col-sm-12">
            <?php
                $lead_stages = $pipeline->leadStages;
                $json = [];
                foreach ($lead_stages as $lead_stage) {
                    $json[] = 'task-list-' . $lead_stage->id;
                }
            ?>
            <div class="kanban-wrapper horizontal-scroll-cards" data-containers='<?php echo json_encode($json); ?>' data-plugin="dragula">
                <?php $__currentLoopData = $lead_stages; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $lead_stage): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                    <?php ($leads = $lead_stage->lead()); ?>
                    <div class="kanban-col crm-sales-card">
                        <div class="kanban-header">
                            <h4><?php echo e($lead_stage->name); ?></h4>
                            <span class="count f-w-600"><?php echo e(count($leads)); ?></span>
                        </div>
                        <div class="sales-item-wrp" id="task-list-<?php echo e($lead_stage->id); ?>" data-id="<?php echo e($lead_stage->id); ?>" data-status="<?php echo e($lead_stage->id); ?>">
                            <?php $__currentLoopData = $leads; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $lead): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                <div class="kanban-card sales-item" data-id="<?php echo e($lead->id); ?>" style="border-top: 4px solid #2e7d32;">
                                    <div class="card-top">
                                        <div class="lead-title">
                                            <a href="<?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('view lead')): ?><?php if($lead->is_active): ?><?php echo e(route('leads.show', $lead->id)); ?><?php else: ?>#<?php endif; ?> <?php else: ?>#<?php endif; ?>" class="dashboard-link">
                                            <i class="fas fa-user me-2"></i><?php echo e($lead->name); ?>

                                            </a>
                                        </div>
                                        <?php if(Auth::user()->type != 'client'): ?>
                                            <div class="btn-group card-option">
                                                <button type="button" class="btn p-0 border-0" data-bs-toggle="dropdown" aria-haspopup="true" aria-expanded="false">
                                                    <i class="ti ti-dots-vertical"></i>
                                                </button>
                                                <div class="dropdown-menu icon-dropdown dropdown-menu-end">
                                                    <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('edit lead')): ?>
                                                        <a href="#" data-size="md" data-url="<?php echo e(URL::to('leads/' . $lead->id . '/labels')); ?>" data-ajax-popup="true" class="dropdown-item" data-bs-original-title="<?php echo e(__('Add Labels')); ?>">
                                                            <i class="ti ti-bookmark"></i>
                                                            <span><?php echo e(__('Labels')); ?></span>
                                                        </a>
                                                        <a href="#" data-size="lg" data-url="<?php echo e(URL::to('leads/' . $lead->id . '/edit')); ?>" data-ajax-popup="true" class="dropdown-item" data-bs-original-title="<?php echo e(__('Edit Lead')); ?>">
                                                            <i class="ti ti-pencil"></i>
                                                            <span><?php echo e(__('Edit')); ?></span>
                                                        </a>
                                                    <?php endif; ?>
                                                    <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('delete lead')): ?>
                                                        <?php echo Form::open([
                                                            'method' => 'DELETE',
                                                            'route' => ['leads.destroy', $lead->id],
                                                            'id' => 'delete-form-' . $lead->id,
                                                        ]); ?>

                                                        <a href="#" class="dropdown-item bs-pass-para">
                                                            <i class="ti ti-trash"></i>
                                                            <span> <?php echo e(__('Delete')); ?> </span>
                                                        </a>
                                                        <?php echo Form::close(); ?>

                                                    <?php endif; ?>
                                                </div>
                                            </div>
                                        <?php endif; ?>
                                    </div>
                                    <div class="badge-wrp">
                                        <?php ($labels = $lead->labels()); ?>
                                        <?php if($labels): ?>
                                            <?php $__currentLoopData = $labels; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $label): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                                <span class="kanban-label bg-light-<?php echo e($label->color); ?>"><?php echo e($label->name); ?></span>
                                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                        <?php endif; ?>
                                    </div>
                                    <?php
                                    $products = $lead->products();
                                    $sources = $lead->sources();
                                    ?>
                                    <div class="contact-info position-relative">
                                        <button type="button" class="position-absolute top-0 end-0 d-inline-flex align-items-center gap-1 p-1 px-2 border rounded-1 bg-light" style="margin-right: 17px;" data-bs-toggle="tooltip" title="<?php echo e(__('Source')); ?>" onclick="openSourcesModal(<?php echo e(json_encode($sources)); ?>)">
                                            <i class="f-16 ti ti-social"></i>
                                            <!-- <?php echo e(count($sources)); ?> -->
                                        </button>
                                        <div class="contact-item text-success">
                                            <i class="fas fa-phone-volume me-1"></i>
                                            <span><?php echo e($lead->phone); ?></span>
                                        </div>
                                        <div class="contact-item text-success">
                                            <i class="fas fa-envelope me-1"></i>
                                            <span><?php echo e($lead->email); ?></span>
                                        </div>
                                    </div>
                                    <div class="card-bottom">
                                        <div class="communication-buttons">
                                            <button class="communication-btn call" data-bs-toggle="tooltip" title="Call">
                                                <i class="fas fa-phone-alt"></i>
                                            </button>
                                            <button class="communication-btn sms" data-bs-toggle="tooltip" title="SMS">
                                                <i class="fas fa-comment-dots"></i>
                                            </button>
                                            <button class="communication-btn email" data-bs-toggle="tooltip" title="Email" onclick="openCommunicationModal(<?php echo e(json_encode(['name' => $lead->name, 'phone' => $lead->phone, 'email' => $lead->email])); ?>)">
                                                <i class="fas fa-envelope"></i>
                                            </button>
                                            <button class="communication-btn email" data-bs-toggle="modal" data-bs-target="#activityModal-<?php echo e($lead->id); ?>" title="Activity">
                                                <i class="fas fa-stream"></i>
                                            </button>
                                        </div>
                                        <div class="user-group">
                                            <?php $__currentLoopData = $lead->users; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $user): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                                <i class="fas fa-user-circle" data-bs-toggle="tooltip" title="<?php echo e($user->name); ?>"></i>
                                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                        </div>
                                    </div>
                                </div>
                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                        </div>
                    </div>
                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
            </div>
        </div>
    </div>

<!-- Communication Modal -->
<div class="modal fade" id="communication-modal" tabindex="-1" aria-labelledby="communication-modal-label" aria-hidden="true">
    <div class="modal-dialog modal-dialog-centered">
        <div class="modal-content modern-comm-modal">
            <div class="modal-header">
                <div class="d-flex align-items-center gap-3">
                    <div id="comm-lead-avatar" class="comm-lead-avatar"></div>
                    <div>
                        <h5 class="modal-title mb-0" id="communication-modal-label"></h5>
                        <div class="comm-lead-name" id="comm-lead-name"></div>
                        <div class="comm-lead-contact text-muted small" id="comm-lead-contact"></div>
                    </div>
                </div>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <div class="communication-options-list">
                    <a href="#" class="communication-option-item" id="whatsapp-option" target="_blank">
                        <i class="fab fa-whatsapp"></i>
                        <span>WhatsApp</span>
                    </a>
                    <a href="#" class="communication-option-item" id="default-email-option">
                        <i class="fas fa-envelope-open-text"></i>
                        <span>Default Email App</span>
                    </a>
                    <a href="#" class="communication-option-item" id="cloud-email-option">
                        <i class="fas fa-cloud-upload-alt"></i>
                        <span>Cloud Email Service</span>
                    </a>
                    <a href="#" class="communication-option-item" id="sms-option">
                        <i class="fas fa-sms"></i>
                        <span>Send SMS</span>
                    </a>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Sources Modal -->
<div class="modal fade" id="sources-modal" tabindex="-1" aria-labelledby="sources-modal-label" aria-hidden="true">
    <div class="modal-dialog modal-dialog-centered">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="sources-modal-label"><?php echo e(__('Lead Sources')); ?></h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <ul class="list-group" id="sources-list">
                    <!-- Sources will be dynamically inserted here -->
                </ul>
            </div>
        </div>
    </div>
</div>

<?php $__currentLoopData = $lead_stages; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $lead_stage): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
    <?php ($leads = $lead_stage->lead()); ?>
    <?php $__currentLoopData = $leads; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $lead): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
        <!-- Activity Modal for this lead -->
        <div class="modal fade" id="activityModal-<?php echo e($lead->id); ?>" tabindex="-1" aria-labelledby="activityModalLabel-<?php echo e($lead->id); ?>" aria-hidden="true">
            <div class="modal-dialog modal-lg modal-dialog-centered">
                <div class="modal-content">
                    <div class="modal-header">
                        <h5 class="modal-title" id="activityModalLabel-<?php echo e($lead->id); ?>"><?php echo e(__('Activity')); ?></h5>
                        <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                    </div>
                    <div class="modal-body">
                        <div class="row leads-scroll">
                            <ul class="event-cards list-group list-group-flush mt-3 w-100">
                                <?php if(!$lead->activities->isEmpty()): ?>
                                    <?php $__currentLoopData = $lead->activities; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $activity): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                        <li class="list-group-item card mb-3">
                                            <div class="row align-items-center justify-content-between">
                                                <div class="col-auto mb-3 mb-sm-0">
                                                    <div class="d-flex align-items-center">
                                                        <div class="theme-avtar bg-primary badge">
                                                            <i class="ti <?php echo e($activity->logIcon()); ?>"></i>
                                                        </div>
                                                        <div class="ms-3">
                                                            <span class="text-dark text-sm"><?php echo e(__($activity->log_type)); ?></span>
                                                            <h6 class="m-0"><?php echo $activity->getLeadRemark(); ?></h6>
                                                            <small class="text-muted"><?php echo e($activity->created_at->diffForHumans()); ?></small>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        </li>
                                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                <?php else: ?>
                                    <li class="text-center py-4">No activity found yet.</li>
                                <?php endif; ?>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
<?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>

    <!-- Filter Off-canvas for Kanban -->
    <div class="offcanvas offcanvas-end" tabindex="-1" id="filterOffcanvasKanban" aria-labelledby="filterOffcanvasKanbanLabel">
        <div class="offcanvas-header">
            <h5 class="offcanvas-title" id="filterOffcanvasKanbanLabel"><?php echo e(__('Filter Leads')); ?></h5>
            <button type="button" class="btn-close" data-bs-dismiss="offcanvas" aria-label="Close"></button>
        </div>
        <div class="offcanvas-body">
            <?php echo e(Form::open(['id' => 'lead-filter-form-kanban', 'onsubmit' => 'return handleKanbanFilterSubmit(this)'])); ?>


            <div class="mb-3">
                <?php echo e(Form::label('name', __('Name'), ['class' => 'form-label'])); ?>

                <?php echo e(Form::text('name', null, ['class' => 'form-control', 'placeholder' => __('Enter lead name')])); ?>

            </div>

            <div class="mb-3">
                <?php echo e(Form::label('email', __('Email'), ['class' => 'form-label'])); ?>

                <?php echo e(Form::email('email', null, ['class' => 'form-control', 'placeholder' => __('Enter email address')])); ?>

            </div>

            <div class="mb-3">
                <?php echo e(Form::label('stage_id', __('Stage'), ['class' => 'form-label'])); ?>

                <?php echo e(Form::select('stage_id', $stages, null, ['class' => 'form-control select', 'placeholder' => __('Select Stage')])); ?>

            </div>

            <div class="d-grid gap-2">
                <input type="submit" value="<?php echo e(__('Apply Filter')); ?>" class="btn btn-primary">
                <input type="button" value="<?php echo e(__('Clear Filters')); ?>" class="btn btn-secondary" onclick="clearKanbanFilters()">
            </div>

            <?php echo e(Form::close()); ?>

        </div>
    </div>

<?php $__env->stopSection(); ?>

<script>
    // Modern openCommunicationModal function
    function openCommunicationModal(lead) {
        const modalEl = document.getElementById('communication-modal');
        const modal = new bootstrap.Modal(modalEl);
        const title = document.getElementById('communication-modal-label');
        const name = document.getElementById('comm-lead-name');
        const contact = document.getElementById('comm-lead-contact');
        const avatar = document.getElementById('comm-lead-avatar');

        // Set modal title
        title.textContent = 'Contact Lead';
        name.textContent = lead.name || '';
        contact.innerHTML =
            (lead.phone ? `<i class='fas fa-phone-alt'></i> ${lead.phone}` : '') +
            (lead.email ? ` &nbsp; <i class='fas fa-envelope'></i> ${lead.email}` : '');
        // Avatar: initials
        if (lead.name) {
            const initials = lead.name.split(' ').map(w => w[0]).join('').substring(0,2).toUpperCase();
            avatar.textContent = initials;
        } else {
            avatar.textContent = '?';
        }
        // WhatsApp
        const whatsapp = document.getElementById('whatsapp-option');
        if (lead.phone) {
            whatsapp.href = `https://wa.me/${lead.phone.replace(/[^\d]/g, '')}`;
            whatsapp.classList.remove('disabled');
        } else {
            whatsapp.href = '#';
            whatsapp.classList.add('disabled');
        }
        // Email (default app)
        const email = document.getElementById('default-email-option');
        if (lead.email) {
            email.href = `mailto:${lead.email}`;
            email.classList.remove('disabled');
        } else {
            email.href = '#';
            email.classList.add('disabled');
        }
        // Cloud email (customize as needed)
        const cloud = document.getElementById('cloud-email-option');
        if (lead.email) {
            cloud.href = `mailto:${lead.email}`;
            cloud.classList.remove('disabled');
        } else {
            cloud.href = '#';
            cloud.classList.add('disabled');
        }
        // SMS
        const sms = document.getElementById('sms-option');
        if (lead.phone) {
            sms.href = `sms:${lead.phone}`;
            sms.classList.remove('disabled');
        } else {
            sms.href = '#';
            sms.classList.add('disabled');
        }
        modal.show();
    }
    // Attach openCommunicationModal to each card's SMS and Email button using event delegation
    document.addEventListener('DOMContentLoaded', function() {
        document.body.addEventListener('click', function(e) {
            // Email button
            if (e.target.closest('.kanban-card .communication-btn.email[onclick^="openCommunicationModal"]')) {
                e.preventDefault();
                const btn = e.target.closest('.kanban-card .communication-btn.email');
                const card = btn.closest('.kanban-card');
                const name = card.querySelector('.lead-title a').textContent.trim();
                const phone = card.querySelector('.contact-item:nth-child(2) span')?.textContent.trim();
                const email = card.querySelector('.contact-item:nth-child(3) span')?.textContent.trim();
                openCommunicationModal({name, phone, email});
            }
            // SMS button
            if (e.target.closest('.kanban-card .communication-btn.sms')) {
                e.preventDefault();
                const btn = e.target.closest('.kanban-card .communication-btn.sms');
                const card = btn.closest('.kanban-card');
                const name = card.querySelector('.lead-title a').textContent.trim();
                const phone = card.querySelector('.contact-item:nth-child(2) span')?.textContent.trim();
                const email = card.querySelector('.contact-item:nth-child(3) span')?.textContent.trim();
                openCommunicationModal({name, phone, email});
            }
        });

        // --- Modal backdrop/blur fix ---
        // Remove any leftover modal-backdrop and reset body styles when any modal is hidden
        document.querySelectorAll('.modal').forEach(function(modal) {
            modal.addEventListener('hidden.bs.modal', function () {
                // Remove all modal-backdrop elements
                document.querySelectorAll('.modal-backdrop').forEach(function(backdrop) {
                    backdrop.parentNode.removeChild(backdrop);
                });
                // Remove modal-open class from body
                document.body.classList.remove('modal-open');
                // Remove any inline style added by Bootstrap
                document.body.style = '';
            });
        });
    });
</script>

<?php echo $__env->make('layouts.admin', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH C:\xampp\htdocs\omx-new-saas\resources\views/leads/index.blade.php ENDPATH**/ ?>