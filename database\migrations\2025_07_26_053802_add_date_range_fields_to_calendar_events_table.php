<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('calendar_events', function (Blueprint $table) {
            // Add date range type field
            $table->enum('date_range_type', ['calendar_days', 'date_range', 'indefinitely'])
                  ->nullable()
                  ->after('status')
                  ->comment('Type of date range: calendar_days, date_range, or indefinitely');

            // Add number of calendar days field
            $table->integer('date_range_days')
                  ->nullable()
                  ->after('date_range_type')
                  ->comment('Number of calendar days into the future (when type is calendar_days)');

            // Add specific date range fields
            $table->date('date_range_start')
                  ->nullable()
                  ->after('date_range_days')
                  ->comment('Start date for date range (when type is date_range)');

            $table->date('date_range_end')
                  ->nullable()
                  ->after('date_range_start')
                  ->comment('End date for date range (when type is date_range)');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('calendar_events', function (Blueprint $table) {
            $table->dropColumn([
                'date_range_type',
                'date_range_days',
                'date_range_start',
                'date_range_end'
            ]);
        });
    }
};
